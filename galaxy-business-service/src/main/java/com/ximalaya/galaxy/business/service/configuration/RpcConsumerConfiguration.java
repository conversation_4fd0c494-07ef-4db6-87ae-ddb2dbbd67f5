package com.ximalaya.galaxy.business.service.configuration;

import com.ximalaya.audit.security.macfarlane.service.IRemoteSyncContentInspectApi;
import com.ximalaya.audit.security.macfarlane.service.impl.ThriftRemoteSyncContentInspectApi;
import com.ximalaya.audit.security.macfarlane.thrift.ContentInspectApi;
import com.ximalaya.eros.mainstay.context.annotation.MainstayClient;
import com.ximalaya.service.profile.thrift.RemoteUserInfoQueryService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * RPC消费者配置
 *
 * <AUTHOR>
 * @create 2024-12-05 13:27
 */
@Configuration
public class RpcConsumerConfiguration {

    @MainstayClient(group = "profile-proxy", autoRegisterJavaIface = true)
    private RemoteUserInfoQueryService.Iface remoteUserInfoQueryService;

    @MainstayClient(group = "audit-macfarlane", timeout = "1000")
    private ContentInspectApi.Iface contentInspectApiIface;

    @Bean
    public IRemoteSyncContentInspectApi contentInspectApi() {
        return new ThriftRemoteSyncContentInspectApi();
    }

}
