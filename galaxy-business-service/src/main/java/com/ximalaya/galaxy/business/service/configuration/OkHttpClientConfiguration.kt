package com.ximalaya.galaxy.business.service.configuration

import okhttp3.OkHttpClient
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.Duration
import java.util.concurrent.TimeUnit

/**
 *<AUTHOR>
 *@create 2023-08-26 11:00
 */
@Configuration
class OkHttpClientConfiguration {

  @Bean(name = ["difyHttpClient"])
  fun difyHttpClient(): OkHttpClient {
    return OkHttpClient.Builder()
      // 设置交互整体不超过 10min
      .callTimeout(Duration.ofMinutes(10))
      // 设置连接超时时间 30s
      .connectTimeout(30, TimeUnit.SECONDS)
      // 设置读取超时时间 5min
      .readTimeout(4, TimeUnit.MINUTES)
      // 设置写入超时时间 1min
      .writeTimeout(1, TimeUnit.MINUTES)
      .build()
  }

  @Bean(name = ["agentHttpClient"])
  fun agentHttpClient(): OkHttpClient {
    return OkHttpClient.Builder()
      // 设置连接超时时间 30s
      .connectTimeout(30, TimeUnit.SECONDS)
      // 设置读取超时时间 1min
      .readTimeout(1, TimeUnit.MINUTES)
      // 设置写入超时时间 1min
      .writeTimeout(1, TimeUnit.MINUTES)
      .build()
  }

}