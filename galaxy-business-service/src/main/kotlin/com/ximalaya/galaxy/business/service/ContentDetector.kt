package com.ximalaya.galaxy.business.service

import com.ximalaya.audit.security.macfarlane.model.InsepectResult
import com.ximalaya.audit.security.macfarlane.model.TextInsepectRequest

/**
 *   <AUTHOR>
 *   @date 2025年05月27日 17:16
 *   @version ContentDetector.java
 */
interface ContentDetector {

    fun detect(text:String): Boolean

    fun detectDetail(text:String): Pair<TextInsepectRequest, InsepectResult>

}