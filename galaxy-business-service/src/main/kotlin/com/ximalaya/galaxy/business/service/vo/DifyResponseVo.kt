package com.ximalaya.hot.track.service.vo

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.annotation.JSONField

/**
 *<AUTHOR>
 *@create 2024-12-04 14:00
 */
class DifyResponseVo {

  @JSONField(name = "task_id")
  var taskId: String? = null

  @JSONField(name = "workflow_run_id")
  var workflowRunId: String? = null

  var data: DifyResponseDataVo? = null

  fun isSuccess(): Boolean {
    return data != null && data!!.status == "succeeded"
  }

  companion object {

    fun parseJson(json: String) = JSON.parseObject(json, DifyResponseVo::class.java)

  }

  override fun toString() =
    "DifyResponseVo(${JSON.toJSONString(this)})"

}

class DifyResponseDataVo {

  var id: String? = null

  @JSONField(name = "workflow_id")
  var workflowId: String? = null

  var status: String? = null

  var outputs: Map<String, String>? = null

  var error: String? = null

  @JSONField(name = "elapsed_time")
  var elapsedTime: Double? = null

  @JSONField(name = "total_tokens")
  var totalTokens: Int? = null

  @JSONField(name = "total_steps")
  var totalSteps: Int? = null

  @JSONField(name = "created_at")
  var createdAt: Long? = null

  @JSONField(name = "finished_at")
  var finishedAt: Long? = null

  override fun toString() =
    "DifyResponseDataVo(${JSON.toJSONString(this)})"

}