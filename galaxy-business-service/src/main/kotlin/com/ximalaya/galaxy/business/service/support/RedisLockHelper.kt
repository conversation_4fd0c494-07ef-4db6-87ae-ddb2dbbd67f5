package com.ximalaya.galaxy.business.service.support

import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.exception.GalaxyException
import mu.KotlinLogging
import org.redisson.api.RLock
import org.redisson.api.RedissonClient
import org.springframework.stereotype.Component
import java.util.concurrent.TimeUnit

/**
 *<AUTHOR>
 *@create 2025-05-20 22:22
 */
@Component
class RedisLockHelper(
    private val redissonClient: RedissonClient
) {

    fun <T> lockAround(
        lockKey: String,
        leaseTime: Long? = null,
        unit: TimeUnit? = null,
        lockBusyCode: ErrorCode? = null,
        lockBusyMessage: String? = null,
        action: () -> T?
    ) =
        doLockAround(
            lockKey = lockKey,
            leaseTime = LEASE_SECONDS_DEFAULT,
            unit = TimeUnit.SECONDS,
            lockBusyMessage,
            action
        )

    fun <T> lockAroundAndCheck(
        lockKey: String,
        leaseTime: Long? = null,
        unit: TimeUnit? = null,
        lockBusyCode: ErrorCode? = null,
        lockBusyMessage: String? = null,
        action: () -> T?
    ): T? {
        val result = doLockAround(
            lockKey,
            leaseTime ?: LEASE_SECONDS_DEFAULT,
            unit ?: TimeUnit.SECONDS,
            lockBusyMessage,
            action
        )

        when (result.first) {
            true -> return result.second
            false -> {
                val finalCode = lockBusyCode ?: ErrorCode.LOCK_BUSY
                val finalMessage = lockBusyMessage ?: finalCode.message

                logger.warn(finalMessage)
                throw GalaxyException(finalCode, finalMessage)
            }
        }
    }

    private fun <T> doLockAround(
        lockKey: String,
        leaseTime: Long,
        unit: TimeUnit,
        lockBusyMessage: String?,
        action: () -> T
    ): Pair<Boolean, T?> {
        val rLock: RLock = redissonClient.getLock(lockKey)

        try {
            if (!rLock.tryLock(WAIT_SECONDS_DEFAULT, leaseTime, unit)) {
                lockBusyMessage?.let {
                    logger.info(it)
                }
                return Pair(false, null)
            }

        } catch (e: InterruptedException) {
            logger.error("RedisLockHelper InterruptedException", e)
            Thread.currentThread().interrupt()
            throw GalaxyException(ErrorCode.THREAD_INTERRUPTED)
        }

        try {
            return Pair(true, action.invoke())
        } catch (e: Exception) {
            throw e
        } finally {
            if (rLock.isLocked && rLock.isHeldByCurrentThread) {
                rLock.unlock()
            }
        }
    }

    companion object {

        private val logger = KotlinLogging.logger { }

        private const val LEASE_SECONDS_DEFAULT = 5L

        private const val WAIT_SECONDS_DEFAULT = 0L

        private const val BASE_LOCK_KEY = "galaxy:lock"

        private const val ONGOING_LOCK_KEY = "$BASE_LOCK_KEY:ongoing"

        private const val CONNECT_LOCK_KEY = "$BASE_LOCK_KEY:connect"

        fun getPhaseOngoingLockKey(sessionCode: Long, phaseCode: Long) =
            "$ONGOING_LOCK_KEY:session:$sessionCode:phase:$phaseCode"

        fun getPhaseConnectLockKey(sessionCode: Long, phaseCode: Long) =
            "$CONNECT_LOCK_KEY:session:$sessionCode:phase:$phaseCode"

    }

}