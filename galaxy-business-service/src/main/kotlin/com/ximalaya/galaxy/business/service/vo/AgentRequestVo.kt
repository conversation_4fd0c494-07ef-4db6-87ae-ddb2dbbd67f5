package com.ximalaya.galaxy.business.service.vo

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.annotation.JSONField
import com.ximalaya.galaxy.business.service.enums.AgentMessageRole
import okhttp3.RequestBody

/**
 *<AUTHOR>
 *@create 2025-05-21 11:18
 */
class AgentChatRequestVo {

  var model = "anthropic.claude-3-7-sonnet-20250219-v1:0"

  var system: String? = null

  var messages: MutableList<AgentMessageVo>? = mutableListOf()

  @JSONField(serialize = false, deserialize = false)
  fun toRequestBody(): RequestBody {
    return RequestBody.create(
      null,
      JSON.toJSONString(this)
    )
  }

}

class AgentMessageVo(
  val _role: AgentMessageRole
) {

  var role = _role.code

  var content: MutableList<AgentContentProtocol>? = mutableListOf()

}