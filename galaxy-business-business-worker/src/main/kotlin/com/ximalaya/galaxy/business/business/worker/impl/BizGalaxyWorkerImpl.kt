package com.ximalaya.galaxy.business.business.worker.impl

import com.alibaba.fastjson.JSON
import com.ximalaya.danube.core.model.DanubeMessage
import com.ximalaya.danube.core.producer.RocketProducer
import com.ximalaya.galaxy.business.business.BizAgent
import com.ximalaya.galaxy.business.business.worker.BizGalaxyWorker
import com.ximalaya.galaxy.business.business.worker.WorkerFootballConfigBean
import com.ximalaya.galaxy.business.business.worker.support.AgentResponseCallback
import com.ximalaya.galaxy.business.common.ALBUM_OUTLINE
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.support.GalaxyAsserts
import com.ximalaya.galaxy.business.repo.entity.GalaxyBlockEntity
import com.ximalaya.galaxy.business.repo.entity.GalaxyPhaseEntity
import com.ximalaya.galaxy.business.repo.entity.GalaxySessionEntity
import com.ximalaya.galaxy.business.repo.enums.AuditState
import com.ximalaya.galaxy.business.repo.enums.BlockType
import com.ximalaya.galaxy.business.repo.enums.LogicDeleted
import com.ximalaya.galaxy.business.repo.enums.PhaseState
import com.ximalaya.galaxy.business.repo.service.GalaxyBlockService
import com.ximalaya.galaxy.business.repo.service.GalaxyPhaseService
import com.ximalaya.galaxy.business.repo.service.GalaxySessionService
import com.ximalaya.galaxy.business.repo.service.GalaxyToolService
import com.ximalaya.galaxy.business.service.ContentDetector
import com.ximalaya.galaxy.business.service.enums.AgentContentType
import com.ximalaya.galaxy.business.service.enums.AgentMessageRole
import com.ximalaya.galaxy.business.service.support.RedisLockHelper
import com.ximalaya.galaxy.business.service.support.getAgentMessageCacheKey
import com.ximalaya.galaxy.business.service.vo.*
import com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse
import com.ximalaya.galaxy.business.worker.api.model.StartSessionJobRequest
import com.ximalaya.galaxy.business.worker.api.model.StartToolJobRequest
import mu.KotlinLogging
import org.apache.commons.collections4.CollectionUtils
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.stereotype.Repository
import java.nio.charset.StandardCharsets
import java.util.*
import java.util.concurrent.RejectedExecutionException
import java.util.concurrent.TimeUnit

/**
 *<AUTHOR>
 *@create 2025-05-20 16:43
 */
@Repository
class BizGalaxyWorkerImpl(
    private val footballConfigBean: WorkerFootballConfigBean,
    @Qualifier("workerExecutor") private val workerExecutor: ThreadPoolTaskExecutor,
    @Qualifier("pg_galaxy_business_worker_topic_galaxy_realtime_messageRocketProducer") private val rocketProducer: RocketProducer,
    private val contentDetector: ContentDetector,
    private val stringRedisTemplate: StringRedisTemplate,
    private val bizAgent: BizAgent,
    private val sessionService: GalaxySessionService,
    private val phaseService: GalaxyPhaseService,
    private val blockService: GalaxyBlockService,
    private val toolService: GalaxyToolService,
) : BizGalaxyWorker {

    private fun aroundJobExecute(
        sessionCode: Long,
        phaseCode: Long,
        jobExecute: (GalaxySessionEntity, GalaxyPhaseEntity, String) -> Unit
    ): CommonJobResponse {
        val response = CommonJobResponse()

        val session = sessionService.selectBySessionCode(sessionCode)
        GalaxyAsserts.assertNotNull(session, ErrorCode.SESSION_ERROR, "会话不存在")

        val phase = phaseService.selectBySessionCodeAndPhaseCode(sessionCode, phaseCode)
        GalaxyAsserts.assertNotNull(phase, ErrorCode.PHASE_ERROR, "阶段不存在")

        val uid = session!!.uid!!

        // todo 限流区分内部、外部
        if (footballConfigBean.isInnerUser(uid)) {
            logger.info("内部用户")
        } else {
            logger.info("外部用户")
        }

        val ongoingRedisKey = RedisLockHelper.getPhaseOngoingLockKey(sessionCode, phaseCode)
        // 使用 setnx 操作替代 bucket，设置有效期为 20 分钟
        val isLockAcquired = stringRedisTemplate.opsForValue()
            .setIfAbsent(ongoingRedisKey, phaseCode.toString(), 20, TimeUnit.MINUTES)
        if (isLockAcquired != true) {
            logger.warn("任务重复执行, Session: {}, Phase: {}", sessionCode, phaseCode)

            response.code = ErrorCode.LOCK_BUSY.code
            response.message = ErrorCode.LOCK_BUSY.message
            return response
        }

        try {
            workerExecutor.execute {
                jobExecute(session, phase!!, ongoingRedisKey)
            }

            response.code = 200
            response.message = "成功"
        } catch (e: RejectedExecutionException) {
            // 执行被拒绝，删除锁
            stringRedisTemplate.delete(ongoingRedisKey)
            response.code = ErrorCode.RATE_LIMIT.code
            response.message = ErrorCode.RATE_LIMIT.message
        }

        return response
    }


    override fun runSessionJob(request: StartSessionJobRequest): CommonJobResponse {
        return aroundJobExecute(request.sessionCode, request.phaseCode) { _, phase, ongoingRedisKey ->
            // 专辑大纲流程
            if (phase.phaseName == ALBUM_OUTLINE) {
                startPhaseJob(request, ongoingRedisKey)
                return@aroundJobExecute
            }

            // todo 专辑大纲流程必须完成了
            val allPhases = phaseService.selectBySessionCode(request.sessionCode)
            val albumOutlinePhase = allPhases.find { it.phaseName == ALBUM_OUTLINE }
            if (albumOutlinePhase == null) {
                rocketProducer.send(
                    buildDanubeMessage(
                        request.sessionCode,
                        request.phaseCode,
                        AgentContentGalaxyException().apply {
                            this.sessionCode = request.sessionCode
                            this.phaseCode = request.phaseCode
                            this.blockCode = getMessageIndex(request.sessionCode, request.phaseCode)
                            this.errorCode = ErrorCode.PHASE_ERROR
                            this.errorMessage = "没有找到专辑大纲流程！！！"
                        })
                )

                // 任务失败，删除锁
                stringRedisTemplate.delete(ongoingRedisKey)
                return@aroundJobExecute
            }

            if (albumOutlinePhase.phaseState == PhaseState.FINISHED.code) {
                startPhaseJob(request, ongoingRedisKey)
                return@aroundJobExecute
            }

            rocketProducer.send(
                buildDanubeMessage(
                    request.sessionCode,
                    request.phaseCode,
                    AgentContentGalaxyException().apply {
                        this.sessionCode = request.sessionCode
                        this.phaseCode = request.phaseCode
                        this.index = getMessageIndex(request.sessionCode, request.phaseCode)
                        this.errorCode = ErrorCode.PHASE_ERROR
                        this.errorMessage = "专辑大纲流程没有完成！！！"
                    })
            )

            // 任务失败，删除锁
            stringRedisTemplate.delete(ongoingRedisKey)
            return@aroundJobExecute
        }
    }

    override fun runToolJob(request: StartToolJobRequest): CommonJobResponse {
        return aroundJobExecute(request.sessionCode, request.phaseCode) { _, _, ongoingRedisKey ->
            val toolEntity = toolService.selectById(request.toolId)
            if (toolEntity == null) {
                logger.warn("工具不存在, Tool: {}", request.toolId)
                return@aroundJobExecute
            }

            // todo 未实现功能
        }
    }

    private fun buildDanubeMessage(
        sessionCode: Long,
        phaseCode: Long,
        agentMessage: AgentContentProtocol
    ): DanubeMessage {
        return DanubeMessage.createRocketMessage(
            null,
            "$sessionCode-$phaseCode",
            agentMessage.toJson().toByteArray(StandardCharsets.UTF_8)
        )
    }

    private fun startPhaseJob(request: StartSessionJobRequest, ongoingRedisKey: String) {
        val blocks: List<GalaxyBlockEntity> = blockService.ktQuery()
            .eq(GalaxyBlockEntity::sessionCode, request.sessionCode)
            .eq(GalaxyBlockEntity::phaseCode, request.phaseCode)
            .eq(GalaxyBlockEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .list()

        // 使用Redis list存储流式结果
        val messageKey = getAgentMessageCacheKey(request.sessionCode, request.phaseCode)

        for (block in blocks) {
            when (BlockType.parseCode(block.blockType)) {
                BlockType.USER -> {
                    stringRedisTemplate.opsForList().rightPush(messageKey, AgentContentBlockUser().apply {
                        this.sessionCode = request.sessionCode
                        this.phaseCode = request.phaseCode
                        this.index = getMessageIndex(request.sessionCode, request.phaseCode)
                        this.blockCode = block.blockCode
                        this.content = block.content
                    }.toJson())
                }

                BlockType.TEXT -> {
                    stringRedisTemplate.opsForList().rightPush(messageKey, AgentContentText().apply {
                        this.sessionCode = request.sessionCode
                        this.phaseCode = request.phaseCode
                        this.index = getMessageIndex(request.sessionCode, request.phaseCode)
                        this.blockCode = block.blockCode
                        this.text = block.content
                    }.toJson())
                }

                BlockType.ERROR, BlockType.EXCEPTION, BlockType.MCP_TOOL_CALL, BlockType.MCP_TOOL_RESULT, BlockType.DIFY_CALL, BlockType.DIFY_RESULT -> {
                    stringRedisTemplate.opsForList()
                        .rightPush(messageKey, AgentContentProtocol.parseJson(block.content!!).toJson())
                }

            }
        }

        // 写入prompt
        val blockCode = blockService.createBlock(request.sessionCode, request.phaseCode, BlockType.USER, request.prompt)

        // 审核prompt
        val detectDetail = contentDetector.detectDetail(request.prompt)
        val detectResponse = detectDetail.second
        val isLegal = detectResponse.result == 0
        if (isLegal) {
            blockService.updateAuditBlockResult(
                request.sessionCode,
                request.phaseCode,
                blockCode,
                AuditState.SUCCESS,
                JSON.toJSONString(detectDetail.first),
                JSON.toJSONString(detectDetail.second)
            )
        } else {
            logger.error(
                "内容审核不通过, Session: {}, Phase: {}, Prompt: {}",
                request.sessionCode,
                request.phaseCode,
                request.prompt
            )
            blockService.updateAuditBlockResult(
                request.sessionCode,
                request.phaseCode,
                blockCode,
                AuditState.FAILED,
                JSON.toJSONString(detectDetail.first),
                JSON.toJSONString(detectDetail.second)
            )
            val errorMessage = AgentContentGalaxyException().apply {
                this.sessionCode = request.sessionCode
                this.phaseCode = request.phaseCode
                this.blockCode = blockCode
                this.errorCode = ErrorCode.CONTENT_ILLEGAL
                this.errorMessage = "内容不合法，已停止生成"
            }
            stringRedisTemplate.opsForList().rightPush(messageKey, errorMessage.toJson())

            rocketProducer.send(
                buildDanubeMessage(
                    request.sessionCode,
                    request.phaseCode,
                    errorMessage
                )
            )

            return
        }

        // 添加用户消息到list
        stringRedisTemplate.opsForList().rightPush(messageKey, AgentContentBlockUser().apply {
            this.sessionCode = request.sessionCode
            this.phaseCode = request.phaseCode
            this.index = getMessageIndex(request.sessionCode, request.phaseCode)
            this.blockCode = blockCode
            this.content = request.prompt
        }.toJson())

        val agentRequest = buildAgentChatRequest(request)
        bizAgent.chat(
            agentRequest,
            AgentResponseCallback(
                request.sessionCode,
                request.phaseCode,
                ongoingRedisKey,
                rocketProducer,
                blockService,
                stringRedisTemplate,
                contentDetector,
            )
        )
    }

    private fun buildAgentChatRequest(request: StartSessionJobRequest): AgentChatRequestVo {
        val messageRedisKey = getAgentMessageCacheKey(request.sessionCode, request.phaseCode)
        val listSize = stringRedisTemplate.opsForList().size(messageRedisKey) ?: 0

        if (listSize > 0) {
            val agentMessageVos = LinkedList<AgentMessageVo>()

            // 从Redis list中获取所有消息
            val cacheMessages = stringRedisTemplate.opsForList().range(messageRedisKey, 0, listSize - 1)

            if (CollectionUtils.isNotEmpty(cacheMessages)) {
                for (messageJson in cacheMessages!!) {
                    val message = AgentContentProtocol.parseJson(messageJson)

                    when (message._type) {
                        AgentContentType.BLOCK_USER -> {
                            agentMessageVos.add(AgentMessageVo(AgentMessageRole.USER).apply {
                                this.content = mutableListOf(
                                    AgentContentText().apply {
                                        this.text = (message as AgentContentBlockUser).content
                                    }
                                )
                            }
                            )
                        }

                        AgentContentType.FINISH, AgentContentType.BLOCK_TEXT, AgentContentType.BLOCK_TOOL_CALL, AgentContentType.BLOCK_TOOL_RESULT,
                        AgentContentType.BLOCK_DIFY_CALL, AgentContentType.BLOCK_DIFY_RESULT -> {
                            continue
                        }

                        AgentContentType.TEXT, AgentContentType.TEXT_DELTA -> {
                            if (agentMessageVos.isEmpty()) {
                                // 添加第一条消息
                                agentMessageVos.add(
                                    AgentMessageVo(AgentMessageRole.GPT).apply {
                                        this.content = mutableListOf(message)
                                    }
                                )
                                continue
                            }
                            val lastMessage = agentMessageVos.last()
                            if (lastMessage._role == AgentMessageRole.GPT) {
                                // 合并
                                val lastContent = lastMessage.content?.last() as AgentContentText
                                if (lastContent._type == AgentContentType.TEXT) {
                                    lastContent.text += (message as AgentContentText).text
                                } else {
                                    lastMessage.content!!.add(message)
                                }
                            } else {
                                // 添加
                                agentMessageVos.add(
                                    AgentMessageVo(AgentMessageRole.GPT).apply {
                                        this.content = mutableListOf(message)
                                    }
                                )
                            }

                        }

                        AgentContentType.TOOL_CALL -> {
                            if (agentMessageVos.isEmpty()) {
                                // 添加第一条消息
                                agentMessageVos.add(AgentMessageVo(AgentMessageRole.GPT).apply {
                                    this.content = mutableListOf(message)
                                })
                                continue
                            }

                            val lastMessage = agentMessageVos.last()
                            if (lastMessage._role == AgentMessageRole.GPT) {
                                lastMessage.content!!.add(message)
                            } else {
                                // 添加
                                agentMessageVos.add(AgentMessageVo(AgentMessageRole.GPT).apply {
                                    this.content = mutableListOf(message)
                                })
                            }

                        }

                        AgentContentType.TOOL_RESULT -> {
                            agentMessageVos.add(AgentMessageVo(AgentMessageRole.TOOL_RESULT).apply {
                                this.content = mutableListOf(message)
                            })
                        }


                        AgentContentType.ERROR, AgentContentType.EXCEPTION -> {
                            val agentMessage = AgentContentGalaxyException().apply {
                                this.sessionCode = request.sessionCode
                                this.phaseCode = request.phaseCode
                                this.index = getMessageIndex(request.sessionCode, request.phaseCode) + 1
                                this.errorCode = ErrorCode.CALL_AGENT_ERROR
                                this.errorMessage = "上下文中发现错误消息"
                            }
                            rocketProducer.send(
                                buildDanubeMessage(
                                    request.sessionCode,
                                    request.phaseCode,
                                    agentMessage
                                )
                            )
                        }
                    }
                }

                return AgentChatRequestVo().apply {
                    this.system = request.systemPrompt
                    this.messages = agentMessageVos
                }
            }
        }

        // 如果Redis中没有缓存或缓存处理失败，则从数据库获取
        val blocks: List<GalaxyBlockEntity> = blockService.ktQuery()
            .eq(GalaxyBlockEntity::sessionCode, request.sessionCode)
            .eq(GalaxyBlockEntity::phaseCode, request.phaseCode)
            .eq(GalaxyBlockEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .list()

        val agentMessageVos = LinkedList<AgentMessageVo>()

        for (block in blocks) {
            when (BlockType.parseCode(block.blockType)) {
                BlockType.USER -> {
                    agentMessageVos.add(
                        AgentMessageVo(AgentMessageRole.USER).apply {
                            this.content = mutableListOf(
                                AgentContentText().apply {
                                    this.blockCode = block.blockCode
                                    this.text = block.content
                                }
                            )
                        }
                    )
                }


                BlockType.TEXT -> {
                    if (agentMessageVos.isNotEmpty()) {
                        val lastMessage = agentMessageVos.last()
                        if (lastMessage._role == AgentMessageRole.GPT) {
                            lastMessage.content!!.add(
                                AgentContentText().apply {
                                    this.blockCode = block.blockCode
                                    this.text = block.content
                                }
                            )
                        } else {
                            agentMessageVos.add(
                                AgentMessageVo(AgentMessageRole.GPT).apply {
                                    this.content = mutableListOf(
                                        AgentContentText().apply {
                                            this.blockCode = block.blockCode
                                            this.text = block.content
                                        }
                                    )
                                }
                            )
                        }
                    } else {
                        agentMessageVos.add(
                            AgentMessageVo(AgentMessageRole.GPT).apply {
                                this.content = mutableListOf(
                                    AgentContentText().apply {
                                        this.blockCode = block.blockCode
                                        this.text = block.content
                                    }
                                )
                            }
                        )
                    }
                }

                BlockType.MCP_TOOL_CALL -> {
                    if (agentMessageVos.isEmpty()) {
                        // 添加
                        agentMessageVos.add(AgentMessageVo(AgentMessageRole.GPT).apply {
                            this.content = mutableListOf(AgentContentProtocol.parseJson(block.content!!))
                        })
                        continue
                    }

                    val lastMessage = agentMessageVos.last()
                    if (lastMessage._role == AgentMessageRole.GPT) {
                        lastMessage.content!!.add(AgentContentProtocol.parseJson(block.content!!))
                    } else {
                        // 添加
                        agentMessageVos.add(AgentMessageVo(AgentMessageRole.GPT).apply {
                            this.content = mutableListOf(AgentContentProtocol.parseJson(block.content!!))
                        })
                    }
                }

                BlockType.MCP_TOOL_RESULT -> {
                    agentMessageVos.add(AgentMessageVo(AgentMessageRole.TOOL_RESULT).apply {
                        this.content = mutableListOf(AgentContentProtocol.parseJson(block.content!!))
                    })
                }

                BlockType.EXCEPTION, BlockType.ERROR -> {
                    val agentMessage = AgentContentGalaxyException().apply {
                        this.sessionCode = request.sessionCode
                        this.phaseCode = request.phaseCode
                        this.index = getMessageIndex(request.sessionCode, request.phaseCode) + 1
                        this.errorCode = ErrorCode.CALL_AGENT_ERROR
                        this.errorMessage = "上下文中发现错误消息"
                    }
                    rocketProducer.send(
                        buildDanubeMessage(
                            request.sessionCode,
                            request.phaseCode,
                            agentMessage
                        )
                    )
                }

                else -> continue
            }
        }

        return AgentChatRequestVo().apply {
            this.system = request.systemPrompt
            this.messages = agentMessageVos
        }
    }

    private fun getMessageIndex(sessionCode: Long, phaseCode: Long): Long {
        return stringRedisTemplate.opsForList().size(getAgentMessageCacheKey(sessionCode, phaseCode)) ?: 0
    }

    companion object {

        private val logger = KotlinLogging.logger { }

    }
}