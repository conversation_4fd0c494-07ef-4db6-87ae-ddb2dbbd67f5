package com.ximalaya.galaxy.business.business.worker.support

import com.alibaba.fastjson.JSON
import com.ximalaya.danube.core.model.DanubeMessage
import com.ximalaya.danube.core.producer.RocketProducer
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.repo.enums.AuditState
import com.ximalaya.galaxy.business.repo.enums.BlockType
import com.ximalaya.galaxy.business.repo.service.GalaxyBlockService
import com.ximalaya.galaxy.business.service.ContentDetector
import com.ximalaya.galaxy.business.service.enums.AgentContentType
import com.ximalaya.galaxy.business.service.support.getAgentMessageCacheKey
import com.ximalaya.galaxy.business.service.vo.AgentContentBlockText
import com.ximalaya.galaxy.business.service.vo.AgentContentBlockToolCall
import com.ximalaya.galaxy.business.service.vo.AgentContentGalaxyException
import com.ximalaya.galaxy.business.service.vo.AgentContentProtocol
import mu.KotlinLogging
import okhttp3.Call
import okhttp3.Callback
import okhttp3.Response
import org.springframework.data.redis.core.StringRedisTemplate
import java.io.IOException
import java.nio.charset.StandardCharsets

/**
 *<AUTHOR>
 *@create 2025-05-22 10:43
 */
class AgentResponseCallback(
    private val sessionCode: Long,
    private val phaseCode: Long,
    private val lockKey: String,
    private val rocketProducer: RocketProducer,
    private val blockService: GalaxyBlockService,
    private val stringRedisTemplate: StringRedisTemplate,
    private val contentDetector: ContentDetector,
) : Callback {

    private fun getMessageIndex(): Long {
        return stringRedisTemplate.opsForList().size(getAgentMessageCacheKey(sessionCode, phaseCode)) ?: 0
    }

    override fun onFailure(call: Call, e: IOException) {
        logger.error("[Agent] IO异常", e)

        val agentMessage = AgentContentGalaxyException().apply {
            this.sessionCode = <EMAIL>
            this.phaseCode = <EMAIL>
            this.index = getMessageIndex() + 1
            this.errorCode = ErrorCode.CALL_AGENT_ERROR
            this.errorMessage = ErrorCode.CALL_AGENT_ERROR.message
        }

        // 存储错误消息到Redis list
        val messageKey = getAgentMessageCacheKey(sessionCode, phaseCode)
        stringRedisTemplate.opsForList().rightPush(messageKey, agentMessage.toJson())

        sendAndRetry(agentMessage)

        // 任务失败，删除锁
        stringRedisTemplate.delete(lockKey)
    }

    override fun onResponse(call: Call, response: Response) {
        response.body()?.source()?.let { source ->
            val messageKey = getAgentMessageCacheKey(sessionCode, phaseCode)
            if (!response.isSuccessful) {
                val agentMessage = AgentContentGalaxyException().apply {
                    this.sessionCode = <EMAIL>
                    this.phaseCode = <EMAIL>
                    this.index = getMessageIndex() + 1
                    this.errorCode = ErrorCode.CALL_AGENT_ERROR
                    this.errorMessage = ErrorCode.CALL_AGENT_ERROR.message
                }

                // 存储错误消息到Redis list
                stringRedisTemplate.opsForList().rightPush(messageKey, agentMessage.toJson())
                sendAndRetry(agentMessage)

                // 任务失败，删除锁
                stringRedisTemplate.delete(lockKey)
                return
            }

            var previousBlockType: BlockType? = null
            var previousBlockCode: Long? = null

            var auditContent = ""

            while (!source.exhausted()) {

                val line = source.readUtf8LineStrict()

                // 处理SSE格式数据
                if (!line.startsWith("data: ")) {
                    continue
                }

                val data = line.substring(6).trim()

                auditContent += data

                val agentMessage = AgentContentProtocol.parseJson(data)

                // 根据类型创建数据块
                when (agentMessage._type) {
                    AgentContentType.TEXT, AgentContentType.TEXT_DELTA -> {
                        if (BlockType.TEXT == previousBlockType) {
                            agentMessage.blockCode = previousBlockCode
                        } else {
                            // 创建块
                            val blockCode = blockService.createBlock(sessionCode, phaseCode, BlockType.TEXT)

                            previousBlockType = BlockType.TEXT
                            previousBlockCode = blockCode

                            // 发送块消息
                            val blockMessage = AgentContentBlockText().apply {
                                this.sessionCode = <EMAIL>
                                this.phaseCode = <EMAIL>
                                this.index = getMessageIndex() + 1
                                this.blockCode = blockCode
                            }

                            // 存储块消息到Redis list
                            stringRedisTemplate.opsForList().rightPush(messageKey, blockMessage.toJson())
                            sendAndRetry(blockMessage)

                            agentMessage.blockCode = blockCode
                        }

                    }

                    AgentContentType.TOOL_CALL -> {
                        // 创建块
                        val blockCode = blockService.createBlock(
                            sessionCode,
                            phaseCode,
                            BlockType.MCP_TOOL_CALL,
                            agentMessage.toJson()
                        )
                        agentMessage.blockCode = blockCode

                        previousBlockType = BlockType.MCP_TOOL_CALL
                        previousBlockCode = blockCode

                        // 发送块消息
                        val blockMessage = AgentContentBlockToolCall().apply {
                            this.sessionCode = <EMAIL>
                            this.phaseCode = <EMAIL>
                            this.index = getMessageIndex() + 1
                            this.blockCode = blockCode
                        }

                        // 存储块消息到Redis list
                        stringRedisTemplate.opsForList().rightPush(messageKey, blockMessage.toJson())
                        sendAndRetry(blockMessage)
                    }

                    AgentContentType.TOOL_RESULT -> {
                        // 创建块
                        val blockCode = blockService.createBlock(
                            sessionCode,
                            phaseCode,
                            BlockType.MCP_TOOL_RESULT,
                            agentMessage.toJson()
                        )
                        agentMessage.blockCode = blockCode

                        previousBlockType = BlockType.MCP_TOOL_CALL
                        previousBlockCode = blockCode

                        // 发送块消息
                        val blockMessage = AgentContentBlockToolCall().apply {
                            this.sessionCode = <EMAIL>
                            this.phaseCode = <EMAIL>
                            this.index = getMessageIndex() + 1
                            this.blockCode = blockCode
                        }

                        // 存储块消息到Redis list
                        stringRedisTemplate.opsForList().rightPush(messageKey, blockMessage.toJson())
                        sendAndRetry(blockMessage)
                    }

                    AgentContentType.ERROR -> {
                        // 创建块
                        val blockCode = blockService.createBlock(
                            sessionCode,
                            phaseCode,
                            BlockType.ERROR,
                            agentMessage.toJson()
                        )

                        // 发送块消息
                        val blockMessage = AgentContentBlockToolCall().apply {
                            this.sessionCode = <EMAIL>
                            this.phaseCode = <EMAIL>
                            this.blockCode = blockCode
                            this.index = getMessageIndex() + 1
                        }

                        // 存储块消息到Redis list
                        stringRedisTemplate.opsForList().rightPush(messageKey, blockMessage.toJson())
                        sendAndRetry(blockMessage)
                    }

                    else -> {}

                }

                agentMessage.sessionCode = <EMAIL>
                agentMessage.phaseCode = <EMAIL>
                agentMessage.index = getMessageIndex() + 1

                auditContent += data
                val auditBlockCodes = mutableSetOf<Long>()
                agentMessage.blockCode?.let {
                    auditBlockCodes.add(it)
                }

                if (auditContent.length >= 100) {
                    // 审核
                    val auditResult = auditText(auditContent, auditBlockCodes)
                    if (auditResult.first) {
                        auditContent = ""
                        auditBlockCodes.clear()
                    } else {
                        val exceptionMessage = auditResult.second!!
                        // 存储错误消息到Redis list
                        stringRedisTemplate.opsForList().rightPush(messageKey, exceptionMessage.toJson())
                        sendAndRetry(exceptionMessage)
                    }

                    // 主动断开HTTP连接
                    call.cancel()
                }

                // 存储消息到Redis list
                stringRedisTemplate.opsForList().rightPush(messageKey, agentMessage.toJson())
                sendAndRetry(agentMessage)

                if (agentMessage._type in setOf(AgentContentType.FINISH, AgentContentType.ERROR)) {
                    // 最后的审核
                    val auditResult = auditText(auditContent, auditBlockCodes)
                    if (!auditResult.first) {
                        val exceptionMessage = auditResult.second!!
                        // 存储错误消息到Redis list
                        stringRedisTemplate.opsForList().rightPush(messageKey, exceptionMessage.toJson())
                        sendAndRetry(exceptionMessage)
                    }

                    // 主动断开HTTP连接
                    call.cancel()
                }
            }
        }
    }

    private fun auditText(
        auditContent: String,
        auditBlockCodes: Set<Long>
    ): Pair<Boolean, AgentContentGalaxyException?> {
        val detectDetail = contentDetector.detectDetail(auditContent)
        val detectResponse = detectDetail.second
        if (detectResponse.result == 0) {
            return Pair(true, null)
        }

        logger.error(
            "内容审核不通过, Session: {}, Phase: {}, Prompt: {}",
            sessionCode,
            phaseCode,
            auditContent
        )

        auditBlockCodes.forEach {
            blockService.updateAuditBlockResult(
                sessionCode,
                phaseCode,
                it,
                AuditState.FAILED,
                JSON.toJSONString(detectDetail.first),
                JSON.toJSONString(detectDetail.second)
            )
        }

        return Pair(false, AgentContentGalaxyException().apply {
            this.sessionCode = <EMAIL>
            this.phaseCode = <EMAIL>
            this.index = getMessageIndex() + 1
            this.errorCode = ErrorCode.CONTENT_ILLEGAL
            this.errorMessage = "内容不合法，已停止生成"
        })
    }

    private fun sendAndRetry(agentMessage: AgentContentProtocol) {
        val maxRetries = 3
        var retryCount = 0

        while (retryCount < maxRetries) {
            try {
                val sendResult = rocketProducer.send(buildDanubeMessage(agentMessage))
                if (sendResult.isSuccess) {
                    return
                }

                logger.warn("[Agent] 消息发送失败，尝试第${retryCount + 1}次重试，sessionCode=$sessionCode, phaseCode=$phaseCode")
            } catch (e: Exception) {
                logger.error(
                    "[Agent] 消息发送异常，尝试第${retryCount + 1}次重试，sessionCode=$sessionCode, phaseCode=$phaseCode",
                    e
                )
            }

            retryCount++
            if (retryCount < maxRetries) {
                // 使用指数退避策略，每次重试间隔增加
                Thread.sleep((100L * (1 shl retryCount)).coerceAtMost(1000L))
            }
        }

        // 所有重试都失败了
        logger.error("[Agent] 消息发送最终失败，已重试${maxRetries}次，sessionCode=$sessionCode, phaseCode=$phaseCode, messageType=${agentMessage._type}")
        // todo 可以考虑将失败的消息保存到数据库或其他持久化存储中，以便后续处理
    }

    private fun buildDanubeMessage(agentMessage: AgentContentProtocol): DanubeMessage {
        return DanubeMessage.createRocketMessage(
            null,
            "$sessionCode-$phaseCode",
            agentMessage.toJson().toByteArray(StandardCharsets.UTF_8)
        )
    }

    companion object {

        private val logger = KotlinLogging.logger { }

    }

}