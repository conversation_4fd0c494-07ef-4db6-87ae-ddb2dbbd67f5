package com.ximalaya.galaxy.business.business.worker

import com.ximalaya.football.client.spring.annotation.FootballConfig
import com.ximalaya.football.client.spring.annotation.FootballField
import com.ximalaya.galaxy.business.common.support.SplitHelper
import org.springframework.stereotype.Component

/**
 *<AUTHOR>
 *@create 2025-05-06 17:24
 */
@FootballConfig("common-config")
@Component
class WorkerFootballConfigBean {

  @FootballField(name = "hello.world", defaultValue = "")
  var helloFootball: String? = null

  @FootballField(name = "user.inner", defaultValue = "")
  var innerUsers: String? = null

  fun getInnerUsers(): List<Long> {
    return SplitHelper.deserialize(innerUsers) { java.lang.Long.valueOf(it) }
  }

  fun isInnerUser(uid: Long): Boolean {
    return getInnerUsers().contains(uid)
  }

}