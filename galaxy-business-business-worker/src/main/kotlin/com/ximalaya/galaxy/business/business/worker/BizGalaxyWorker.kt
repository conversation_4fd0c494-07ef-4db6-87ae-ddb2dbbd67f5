package com.ximalaya.galaxy.business.business.worker

import com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse
import com.ximalaya.galaxy.business.worker.api.model.StartSessionJobRequest
import com.ximalaya.galaxy.business.worker.api.model.StartToolJobRequest

/**
 *<AUTHOR>
 *@create 2025-05-20 10:54
 */
interface BizGalaxyWorker {

  fun runSessionJob(request: StartSessionJobRequest): CommonJobResponse

  fun runToolJob(request: StartToolJobRequest): CommonJobResponse

}