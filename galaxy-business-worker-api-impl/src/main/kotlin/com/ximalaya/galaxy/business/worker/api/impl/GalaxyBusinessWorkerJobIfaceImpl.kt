package com.ximalaya.galaxy.business.worker.api.impl

import com.ximalaya.eros.mainstay.context.annotation.MainstayServer
import com.ximalaya.galaxy.business.business.worker.BizGalaxyWorker
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.exception.GalaxyException
import com.ximalaya.galaxy.business.worker.api.converter.CommonJobResponseConverter
import com.ximalaya.galaxy.business.worker.api.converter.StartSessionJobRequestConverter
import com.ximalaya.galaxy.business.worker.api.converter.StartToolJobRequestConverter
import com.ximalaya.galaxy.business.worker.api.thrift.GalaxyBusinessWorkerJobService
import com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse
import com.ximalaya.galaxy.business.worker.api.thrift.entity.StartSessionJobRequest
import com.ximalaya.galaxy.business.worker.api.thrift.entity.StartToolJobRequest

/**
 *<AUTHOR>
 *@create 2024-11-28 11:58
 */
@MainstayServer(group = "galaxy-business-worker")
open class GalaxyBusinessWorkerJobIfaceImpl(
    private val bizGalaxyWorker: BizGalaxyWorker
) : GalaxyBusinessWorkerJobService.Iface {

    override fun startSessionJob(startRequest: StartSessionJobRequest?): CommonJobResponse {
        val request = StartSessionJobRequestConverter.transform(startRequest)

        return doStartJob {
            CommonJobResponseConverter.transform(bizGalaxyWorker.runSessionJob(request))
        }
    }

    override fun startToolJob(startRequest: StartToolJobRequest?): CommonJobResponse {
        val request = StartToolJobRequestConverter.transform(startRequest)

        return doStartJob {
            CommonJobResponseConverter.transform(bizGalaxyWorker.runToolJob(request))
        }
    }

    private fun doStartJob(task: () -> CommonJobResponse): CommonJobResponse {
        val response = com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse()

        try {
            return task.invoke()
        } catch (ex: GalaxyException) {
            val errorCode = ex.errorCode ?: ErrorCode.UNKNOWN_ERROR
            val message = ex.message ?: errorCode.message

            response.code = errorCode.code
            response.message = message
            return CommonJobResponseConverter.transform(response)
        }
    }

}