CREATE TABLE `gxy_invitation_code`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `code`             varchar(30) NOT NULL COMMENT '邀请码',
    `code_state`       tinyint(1)  NOT NULL COMMENT '是否被删除',
    `uid`              varchar(30)          DEFAULT NULL COMMENT '注册uid',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `uniq_code` (`code`),
    UNIQUE INDEX `uniq_uid` (`uid`)
) ENGINE = InnoDB COMMENT '邀请码';

CREATE TABLE `gxy_prompt`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name`             varchar(50) NOT NULL COMMENT '名称',
    `content`          varchar(30) NOT NULL COMMENT '内容',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `uniq_name` (`name`)
) ENGINE = InnoDB COMMENT 'prompt';

CREATE TABLE `gxy_tool`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `tool_name`        varchar(50) NOT NULL COMMENT '名称',
    `tool_type`        tinyint(4)  NOT NULL COMMENT '工具类型 1=Dify',
    `meta_data`        text                 DEFAULT NULL COMMENT '元数据',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `uniq_tool_name` (`tool_name`)
) ENGINE = InnoDB COMMENT 'Galaxy工具表';

-- gxy_session 分表
CREATE TABLE `gxy_session_202501`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `session_state`    tinyint(4)           DEFAULT 0 COMMENT '会话状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid` (`uid`),
    UNIQUE INDEX `uni_session_code` (`session_code`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表202501';

CREATE TABLE `gxy_session_202502`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `session_state`    tinyint(4)           DEFAULT 0 COMMENT '会话状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid` (`uid`),
    UNIQUE INDEX `uni_session_code` (`session_code`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表202502';

CREATE TABLE `gxy_session_202503`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `session_state`    tinyint(4)           DEFAULT 0 COMMENT '会话状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid` (`uid`),
    UNIQUE INDEX `uni_session_code` (`session_code`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表202503';

CREATE TABLE `gxy_session_202504`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `session_state`    tinyint(4)           DEFAULT 0 COMMENT '会话状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid` (`uid`),
    UNIQUE INDEX `uni_session_code` (`session_code`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表202504';

CREATE TABLE `gxy_session_202505`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `session_state`    tinyint(4)           DEFAULT 0 COMMENT '会话状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid` (`uid`),
    UNIQUE INDEX `uni_session_code` (`session_code`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表202505';

CREATE TABLE `gxy_session_202506`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `session_state`    tinyint(4)           DEFAULT 0 COMMENT '会话状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid` (`uid`),
    UNIQUE INDEX `uni_session_code` (`session_code`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表202506';

CREATE TABLE `gxy_session_202507`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `session_state`    tinyint(4)           DEFAULT 0 COMMENT '会话状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid` (`uid`),
    UNIQUE INDEX `uni_session_code` (`session_code`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表202507';

CREATE TABLE `gxy_session_202508`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `session_state`    tinyint(4)           DEFAULT 0 COMMENT '会话状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid` (`uid`),
    UNIQUE INDEX `uni_session_code` (`session_code`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表202508';

CREATE TABLE `gxy_session_202509`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `session_state`    tinyint(4)           DEFAULT 0 COMMENT '会话状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid` (`uid`),
    UNIQUE INDEX `uni_session_code` (`session_code`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表202509';

CREATE TABLE `gxy_session_202510`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `session_state`    tinyint(4)           DEFAULT 0 COMMENT '会话状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid` (`uid`),
    UNIQUE INDEX `uni_session_code` (`session_code`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表202510';

CREATE TABLE `gxy_session_202511`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `session_state`    tinyint(4)           DEFAULT 0 COMMENT '会话状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid` (`uid`),
    UNIQUE INDEX `uni_session_code` (`session_code`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表202511';

CREATE TABLE `gxy_session_202512`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `session_state`    tinyint(4)           DEFAULT 0 COMMENT '会话状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid` (`uid`),
    UNIQUE INDEX `uni_session_code` (`session_code`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表202512';

CREATE TABLE `gxy_session_202601`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `session_state`    tinyint(4)           DEFAULT 0 COMMENT '会话状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid` (`uid`),
    UNIQUE INDEX `uni_session_code` (`session_code`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表202601';

CREATE TABLE `gxy_session_202602`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `session_state`    tinyint(4)           DEFAULT 0 COMMENT '会话状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid` (`uid`),
    UNIQUE INDEX `uni_session_code` (`session_code`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表202602';

CREATE TABLE `gxy_session_202603`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `session_state`    tinyint(4)           DEFAULT 0 COMMENT '会话状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid` (`uid`),
    UNIQUE INDEX `uni_session_code` (`session_code`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表202603';

CREATE TABLE `gxy_session_202604`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `session_state`    tinyint(4)           DEFAULT 0 COMMENT '会话状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid` (`uid`),
    UNIQUE INDEX `uni_session_code` (`session_code`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表202604';

CREATE TABLE `gxy_session_202605`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `session_state`    tinyint(4)           DEFAULT 0 COMMENT '会话状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid` (`uid`),
    UNIQUE INDEX `uni_session_code` (`session_code`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表202605';

CREATE TABLE `gxy_session_202606`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `session_state`    tinyint(4)           DEFAULT 0 COMMENT '会话状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid` (`uid`),
    UNIQUE INDEX `uni_session_code` (`session_code`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表202606';

CREATE TABLE `gxy_session_202607`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `session_state`    tinyint(4)           DEFAULT 0 COMMENT '会话状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid` (`uid`),
    UNIQUE INDEX `uni_session_code` (`session_code`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表202607';

CREATE TABLE `gxy_session_202608`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `session_state`    tinyint(4)           DEFAULT 0 COMMENT '会话状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid` (`uid`),
    UNIQUE INDEX `uni_session_code` (`session_code`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表202608';

CREATE TABLE `gxy_session_202609`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `session_state`    tinyint(4)           DEFAULT 0 COMMENT '会话状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid` (`uid`),
    UNIQUE INDEX `uni_session_code` (`session_code`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表202609';

CREATE TABLE `gxy_session_202610`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `session_state`    tinyint(4)           DEFAULT 0 COMMENT '会话状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid` (`uid`),
    UNIQUE INDEX `uni_session_code` (`session_code`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表202610';

CREATE TABLE `gxy_session_202611`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `session_state`    tinyint(4)           DEFAULT 0 COMMENT '会话状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid` (`uid`),
    UNIQUE INDEX `uni_session_code` (`session_code`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表202611';

CREATE TABLE `gxy_session_202612`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `uid`              bigint(20)  NOT NULL COMMENT '用户ID',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话编码（雪花ID）',
    `session_name`     varchar(255)         DEFAULT NULL COMMENT '会话名称',
    `session_state`    tinyint(4)           DEFAULT 0 COMMENT '会话状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_uid` (`uid`),
    UNIQUE INDEX `uni_session_code` (`session_code`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE = InnoDB COMMENT 'Galaxy会话表202612';

-- gxy_phase 分表 (0-19)
CREATE TABLE `gxy_phase_0`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表0';

CREATE TABLE `gxy_phase_1`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表1';

CREATE TABLE `gxy_phase_2`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表2';

CREATE TABLE `gxy_phase_3`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表3';

CREATE TABLE `gxy_phase_4`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表4';

CREATE TABLE `gxy_phase_5`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表5';

CREATE TABLE `gxy_phase_6`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表6';

CREATE TABLE `gxy_phase_7`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表7';

CREATE TABLE `gxy_phase_8`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表8';

CREATE TABLE `gxy_phase_9`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表9';

CREATE TABLE `gxy_phase_10`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表10';

CREATE TABLE `gxy_phase_11`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表11';

CREATE TABLE `gxy_phase_12`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表12';

CREATE TABLE `gxy_phase_13`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表13';

CREATE TABLE `gxy_phase_14`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表14';

CREATE TABLE `gxy_phase_15`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表15';

CREATE TABLE `gxy_phase_16`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表16';

CREATE TABLE `gxy_phase_17`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表17';

CREATE TABLE `gxy_phase_18`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表18';

CREATE TABLE `gxy_phase_19`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码（雪花ID）',
    `phase_name`       varchar(128)         DEFAULT NULL COMMENT '阶段名称',
    `phase_state`      tinyint(4)           DEFAULT 0 COMMENT '阶段状态 0=进行中 1=已结束 -1=失败',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    UNIQUE INDEX `uniq_phase_code` (`phase_code`)
) ENGINE = InnoDB COMMENT 'Galaxy阶段表19';

-- gxy_block 分表 (0-19)
CREATE TABLE `gxy_block_0`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表0';

CREATE TABLE `gxy_block_1`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表1';

CREATE TABLE `gxy_block_2`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表2';

CREATE TABLE `gxy_block_3`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表3';

CREATE TABLE `gxy_block_4`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表4';

CREATE TABLE `gxy_block_5`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表5';

CREATE TABLE `gxy_block_6`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表6';

CREATE TABLE `gxy_block_7`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表7';

CREATE TABLE `gxy_block_8`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表8';

CREATE TABLE `gxy_block_9`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表9';

CREATE TABLE `gxy_block_10`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表10';

CREATE TABLE `gxy_block_11`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表11';

CREATE TABLE `gxy_block_12`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表12';

CREATE TABLE `gxy_block_13`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表13';

CREATE TABLE `gxy_block_14`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表14';

CREATE TABLE `gxy_block_15`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表15';

CREATE TABLE `gxy_block_16`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表16';

CREATE TABLE `gxy_block_17`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表17';

CREATE TABLE `gxy_block_18`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表18';

CREATE TABLE `gxy_block_19`
(
    `id`               bigint(20)  NOT NULL AUTO_INCREMENT COMMENT '主键',
    `session_code`     bigint(20)  NOT NULL COMMENT '会话ID',
    `phase_code`       bigint(20)  NOT NULL COMMENT '阶段编码',
    `block_code`       bigint(20)  NOT NULL COMMENT '块编码（雪花ID）',
    `block_type`       tinyint(4)  NOT NULL COMMENT '块类型',
    `response`         MEDIUMTEXT           DEFAULT NULL COMMENT '响应内容',
    `content`          MEDIUMTEXT           DEFAULT NULL COMMENT '格式化后的内容',
    `audit_state`      tinyint(4)           DEFAULT 0 COMMENT '审核状态 0=未审核 1=审核通过 -1=审核不通过',
    `audit_request`    MEDIUMTEXT           DEFAULT NULL COMMENT '审核请求',
    `audit_response`   MEDIUMTEXT           DEFAULT NULL COMMENT '审核响应',
    `deleted_at`       int(11)              DEFAULT NULL COMMENT '删除时间',
    `create_time`      datetime(3) NOT NULL COMMENT '创建时间',
    `update_time`      datetime(3) NOT NULL COMMENT '修改时间',
    `data_create_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '数据库创建时间',
    `data_update_time` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '数据库修改时间',
    PRIMARY KEY (`id`),
    INDEX `idx_session_code` (`session_code`),
    INDEX `idx_phase_code` (`phase_code`),
    UNIQUE INDEX `uniq_block_code` (`block_code`)
) ENGINE = InnoDB COMMENT 'Galaxy块分表19';