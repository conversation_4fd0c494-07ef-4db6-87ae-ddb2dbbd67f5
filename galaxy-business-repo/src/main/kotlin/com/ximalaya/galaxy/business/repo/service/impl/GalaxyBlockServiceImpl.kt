package com.ximalaya.galaxy.business.repo.service.impl

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.support.GalaxyAsserts
import com.ximalaya.galaxy.business.repo.entity.GalaxyBlockEntity
import com.ximalaya.galaxy.business.repo.enums.AuditState
import com.ximalaya.galaxy.business.repo.enums.BlockType
import com.ximalaya.galaxy.business.repo.enums.LogicDeleted
import com.ximalaya.galaxy.business.repo.mapper.GalaxyBlockMapper
import com.ximalaya.galaxy.business.repo.service.GalaxyBlockService
import org.springframework.stereotype.Repository
import java.time.LocalDateTime

/**
 *<AUTHOR>
 *@create 2025-05-19 15:32
 */
@Repository
class GalaxyBlockServiceImpl : ServiceImpl<GalaxyBlockMapper, GalaxyBlockEntity>(), GalaxyBlockService {

    override fun createBlock(sessionCode: Long, phaseCode: Long, blockType: BlockType, content: String?): Long {
        val now = LocalDateTime.now()

        val blockEntity = GalaxyBlockEntity().apply {
            this.sessionCode = sessionCode
            this.phaseCode = phaseCode
            this.blockType = blockType.code
            this.content = content
            this.auditState = AuditState.INIT.code
            this.deletedAt = LogicDeleted.SAVE.getCode()
            this.createTime = now
            this.updateTime = now
        }

        GalaxyAsserts.assertTrue(save(blockEntity), ErrorCode.BLOCK_ERROR, "创建数据块失败")
        // blockCode
        return blockEntity.id!!
    }

    override fun updateBlock(sessionCode: Long, phaseCode: Long, blockCode: Long, content: String): Boolean {
        return ktUpdate().eq(GalaxyBlockEntity::sessionCode, sessionCode)
            .eq(GalaxyBlockEntity::phaseCode, phaseCode)
            .eq(GalaxyBlockEntity::blockCode, blockCode)
            .set(GalaxyBlockEntity::content, content)
            .update()
    }

    override fun updateAuditBlockResult(
        sessionCode: Long,
        phaseCode: Long,
        blockCode: Long,
        auditState: AuditState,
        auditRequest: String,
        auditResponse: String
    ): Boolean {
        return ktUpdate().eq(GalaxyBlockEntity::sessionCode, sessionCode)
            .eq(GalaxyBlockEntity::phaseCode, phaseCode)
            .eq(GalaxyBlockEntity::blockCode, blockCode)
            .set(GalaxyBlockEntity::auditState, auditState.code)
            .set(GalaxyBlockEntity::auditRequest, auditRequest)
            .set(GalaxyBlockEntity::auditResponse, auditResponse)
            .update()
    }

}