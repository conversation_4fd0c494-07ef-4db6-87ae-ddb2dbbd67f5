package com.ximalaya.galaxy.business.repo.enums

import com.ximalaya.galaxy.business.common.enums.Enums

/**
 *<AUTHOR>
 *@create 2025-05-14 14:33
 */
enum class SessionState(
  val code: Int,
  val desc: String
) {

  INIT(0, "初始化"),
  RUNNING(1, "进行中"),
  FINISHED(2, "已结束"),
  FAILED(-1, "失败"),
  ;

  companion object {

    @JvmStatic
    fun parseCode(code: Int?): SessionState {
      return Enums.parseNotNull(
        SessionState.values(),
        SessionState::code,
        code,
        "Enum not support SessionState: $code"
      )
    }

  }
}