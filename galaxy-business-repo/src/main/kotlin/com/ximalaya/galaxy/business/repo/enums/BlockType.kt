package com.ximalaya.galaxy.business.repo.enums

import com.ximalaya.galaxy.business.common.enums.Enums

/**
 *<AUTHOR>
 *@create 2025-05-15 11:12
 */
enum class BlockType(
  val code: Int,
  val desc: String
) {

  EXCEPTION(-2, "系统异常"),
  ERROR(-1, "Agent错误"),
  USER(0, "用户输入"),
  TEXT(1, "文本"),
  MCP_TOOL_CALL(2, "MCP Call"),
  MCP_TOOL_RESULT(2, "MCP Result"),
  DIFY_CALL(3, "Dify Call"),
  DIFY_RESULT(4, "Dify结果"),
  ;

  companion object {

    @JvmStatic
    fun parseCode(code: Int?): BlockType {
      return Enums.parseNotNull(
        BlockType.values(),
        BlockType::code,
        code,
        "Enum not support BlockType: $code"
      )
    }

  }

}