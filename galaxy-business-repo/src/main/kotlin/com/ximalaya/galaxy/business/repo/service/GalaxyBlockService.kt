package com.ximalaya.galaxy.business.repo.service

import com.baomidou.mybatisplus.extension.service.IService
import com.ximalaya.galaxy.business.repo.entity.GalaxyBlockEntity
import com.ximalaya.galaxy.business.repo.enums.AuditState
import com.ximalaya.galaxy.business.repo.enums.BlockType

/**
 *<AUTHOR>
 *@create 2025-05-19 15:32
 */
interface GalaxyBlockService : IService<GalaxyBlockEntity> {

    fun createBlock(sessionCode: Long, phaseCode: Long, blockType: BlockType, content: String? = null): Long

    fun updateBlock(sessionCode: Long, phaseCode: Long, blockCode: Long, content: String): Boolean

    fun updateAuditBlockResult(
        sessionCode: Long,
        phaseCode: Long,
        blockCode: Long,
        auditState: AuditState,
        auditRequest: String,
        auditResponse: String,
    ): Boolean

}