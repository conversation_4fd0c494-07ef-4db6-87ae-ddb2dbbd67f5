package com.ximalaya.galaxy.business.repo.entity

import com.baomidou.mybatisplus.annotation.TableName
import com.ximalaya.galaxy.business.repo.enums.InvitationCodeState
import java.time.LocalDateTime

/**
 *<AUTHOR>
 *@create 2023-08-29 15:09
 */
@TableName("gxy_invitation_code")
data class InvitationCodeEntity(
  var id: Long? = null,

  var code: String? = null,

  /**
   * @see com.ximalaya.galaxy.business.repo.enums.InvitationCodeState
   */
  var codeState: Int? = null,

  var uid: Long? = null,

  var createTime: LocalDateTime? = null,
  var updateTime: LocalDateTime? = null,

  var dataCreateTime: LocalDateTime? = null,
  var dataUpdateTime: LocalDateTime? = null,
) {

  companion object {

    fun of(codes: Collection<String>): List<InvitationCodeEntity> {
      val now = LocalDateTime.now()
      return codes.map {
        InvitationCodeEntity().apply {
          this.code = it
          this.codeState = InvitationCodeState.UNUSED.code
          this.createTime = now
          this.updateTime = now
        }
      }
    }

  }
}