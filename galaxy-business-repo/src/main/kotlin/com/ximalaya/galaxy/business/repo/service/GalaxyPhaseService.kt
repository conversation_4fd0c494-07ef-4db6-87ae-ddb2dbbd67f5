package com.ximalaya.galaxy.business.repo.service

import com.baomidou.mybatisplus.extension.service.IService
import com.ximalaya.galaxy.business.repo.entity.GalaxyPhaseEntity

/**
 *<AUTHOR>
 *@create 2025-05-19 15:32
 */
interface GalaxyPhaseService : IService<GalaxyPhaseEntity> {

  fun selectBySessionCode(sessionCode: Long): List<GalaxyPhaseEntity>

  fun selectBySessionCodeAndPhaseCode(sessionCode: Long, phaseCode: Long): GalaxyPhaseEntity?

}