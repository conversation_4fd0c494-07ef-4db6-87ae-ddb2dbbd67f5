package com.ximalaya.galaxy.business.repo.service.impl

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.ximalaya.galaxy.business.repo.entity.GalaxyPhaseEntity
import com.ximalaya.galaxy.business.repo.enums.LogicDeleted
import com.ximalaya.galaxy.business.repo.mapper.GalaxyPhaseMapper
import com.ximalaya.galaxy.business.repo.service.GalaxyPhaseService
import org.springframework.stereotype.Repository

/**
 *<AUTHOR>
 *@create 2025-05-19 15:32
 */
@Repository
class GalaxyPhaseServiceImpl : ServiceImpl<GalaxyPhaseMapper, GalaxyPhaseEntity>(), GalaxyPhaseService {

  override fun selectBySessionCode(sessionCode: Long): List<GalaxyPhaseEntity> {
    return ktQuery().eq(GalaxyPhaseEntity::sessionCode, sessionCode)
      .eq(GalaxyPhaseEntity::deletedAt, LogicDeleted.SAVE.getCode())
      .list()
  }

  override fun selectBySessionCodeAndPhaseCode(sessionCode: Long, phaseCode: Long): GalaxyPhaseEntity? {
    return ktQuery().eq(GalaxyPhaseEntity::sessionCode, sessionCode)
      .eq(GalaxyPhaseEntity::phaseCode, phaseCode)
      .eq(GalaxyPhaseEntity::deletedAt, LogicDeleted.SAVE.getCode())
      .one()
  }

}