package com.ximalaya.galaxy.business.repo.algorithm

import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.support.DateTimes
import com.ximalaya.galaxy.business.common.support.GalaxyAsserts
import com.ximalaya.galaxy.business.repo.support.GALAXY_SESSION_TABLE_NAME
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingAlgorithm
import org.apache.shardingsphere.api.sharding.standard.PreciseShardingValue
import java.sql.Timestamp

/**
 *<AUTHOR>
 *@create 2025-05-19 15:01
 */
class GalaxySessionYearMonthPreciseShardingAlgorithm : PreciseShardingAlgorithm<Timestamp> {

  override fun doSharding(tableNames: MutableCollection<String>, shardingValue: PreciseShardingValue<Timestamp>): String {
    val dateTime = shardingValue.value.toLocalDateTime()

    // 根据时间字段计算表名后缀（如202505）
    val targetTable = "${GALAXY_SESSION_TABLE_NAME}_${dateTime.format(DateTimes.COMPACT_YEAR_MONTH)}"
    GalaxyAsserts.assertTrue(tableNames.contains(targetTable), ErrorCode.CONTENT_NOT_FOUND, "表不存在: $targetTable")

    return targetTable
  }

}