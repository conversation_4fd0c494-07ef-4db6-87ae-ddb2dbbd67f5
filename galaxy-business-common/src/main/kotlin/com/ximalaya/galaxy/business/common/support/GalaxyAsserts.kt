package com.ximalaya.galaxy.business.common.support

import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.exception.GalaxyException
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.collections4.MapUtils
import org.apache.commons.lang3.StringUtils
import java.util.*

/**
 *<AUTHOR>
 *@create 2023-07-30 16:25
 */
object GalaxyAsserts {

  @JvmStatic
  fun assertTrue(condition: <PERSON>olean, code: ErrorCode? = null, message: String? = null) {
    if (!condition) {
      throwException(code, message)
    }
  }

  @JvmStatic
  fun assertFalse(condition: <PERSON>ole<PERSON>, code: ErrorCode? = null, message: String? = null) {
    if (condition) {
      throwException(code, message)
    }
  }

  @JvmStatic
  fun assertEquals(x: Any?, y: Any?, code: ErrorCode? = null, message: String? = null) {
    if (!Objects.equals(x, y)) {
      throwException(code, message)
    }
  }

  @JvmStatic
  fun assertNotNull(any: Any?, code: ErrorCode? = null, message: String? = null) {
    if (Objects.isNull(any)) {
      throwException(code, message)
    }
  }

  @JvmStatic
  fun assertNull(any: Any?, code: ErrorCode? = null, message: String? = null) {
    if (Objects.nonNull(any)) {
      throwException(code, message)
    }
  }

  @JvmStatic
  fun assertNotBlank(str: String?, code: ErrorCode? = null, message: String? = null) {
    if (StringUtils.isBlank(str)) {
      throwException(code, message)
    }
  }

  @JvmStatic
  fun <T> assertNotEmpty(collection: Collection<T>?, code: ErrorCode? = null, message: String? = null) {
    if (CollectionUtils.isEmpty(collection)) {
      throwException(code, message)
    }
  }

  @JvmStatic
  fun <K, V> assertNotEmpty(map: Map<K, V>, code: ErrorCode? = null, message: String? = null) {
    if (MapUtils.isEmpty(map)) {
      throwException(code, message)
    }
  }

  @JvmStatic
  fun <T> assertEmpty(collection: Collection<T>?, code: ErrorCode? = null, message: String? = null) {
    if (CollectionUtils.isNotEmpty(collection)) {
      throwException(code, message)
    }
  }

  @JvmStatic
  fun <K, V> assertEmpty(map: Map<K, V>, code: ErrorCode? = null, message: String? = null) {
    if (MapUtils.isNotEmpty(map)) {
      throwException(code, message)
    }
  }

  @JvmStatic
  fun <T> requireNonNull(obj: T?): T {
    assertNotNull(obj)
    return obj!!
  }

  @JvmStatic
  fun <T> requireNonNull(obj: T?, code: ErrorCode?): T {
    assertNotNull(obj, code)
    return obj!!
  }

  @JvmStatic
  fun <T> requireNonNull(obj: T?, code: ErrorCode? = null, message: String? = null): T {
    assertNotNull(obj, code, message)
    return obj!!
  }

  private fun throwException(code: ErrorCode?, message: String?) {
    val finallyCode = code ?: ErrorCode.UNKNOWN_ERROR

    val finallyMessage = if (StringUtils.isBlank(message)) {
      finallyCode.message
    } else message

    throw GalaxyException(finallyCode, finallyMessage)
  }

}