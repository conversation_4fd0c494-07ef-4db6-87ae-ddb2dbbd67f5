package com.ximalaya.galaxy.business.worker.configuration;

import com.ximalaya.galaxy.business.worker.interceptor.HttpRequestLogFilter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.data.convert.Jsr310Converters.StringToLocalDateConverter;
import org.springframework.data.convert.Jsr310Converters.StringToLocalDateTimeConverter;
import org.springframework.format.FormatterRegistry;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.servlet.MultipartConfigElement;

/**
 * <AUTHOR>
 * @create 2023-08-04 01:37
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class WebConfiguration implements WebMvcConfigurer {

  /**
   * 处理开发测试环境跨域请求, 方便联调
   */
  @Bean
  @Profile({"dev"})
  public FilterRegistrationBean<CorsFilter> corsFilter() {
    UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
    CorsConfiguration config = new CorsConfiguration();
    config.setAllowCredentials(true);
    config.addAllowedOriginPattern(CorsConfiguration.ALL);
    config.addAllowedHeader(CorsConfiguration.ALL);
    config.addAllowedMethod(CorsConfiguration.ALL);
    // CORS 配置对所有接口都有效
    source.registerCorsConfiguration("/**", config);
    FilterRegistrationBean<CorsFilter> registrationBean = new FilterRegistrationBean<>();
    CorsFilter corsFilter = new CorsFilter(source);
    registrationBean.setFilter(corsFilter);
    registrationBean.setOrder(0);
    return registrationBean;
  }

  @Bean
  public MultipartConfigElement multipartConfig() {
    int maxSize = 100 * 1024 * 1024;
    return new MultipartConfigElement("/tmp", maxSize, maxSize, maxSize);
  }

  @Bean
  @Profile({"dev", "test"})
  public FilterRegistrationBean<HttpRequestLogFilter> httpRequestLogFilter() {
    FilterRegistrationBean<HttpRequestLogFilter> registrationBean = new FilterRegistrationBean<>();
    registrationBean.setFilter(new HttpRequestLogFilter());
    registrationBean.addUrlPatterns("/*");
    return registrationBean;
  }

  @Override
  public void addResourceHandlers(ResourceHandlerRegistry registry) {
    registry.addResourceHandler("swagger-ui.html")
        .addResourceLocations("classpath:/META-INF/resources/");
    registry.addResourceHandler("/webjars/**")
        .addResourceLocations("classpath:/META-INF/resources/webjars/");
  }

  @Override
  public void addFormatters(FormatterRegistry registry) {
    registry.addConverter(StringToLocalDateTimeConverter.INSTANCE);
    registry.addConverter(StringToLocalDateConverter.INSTANCE);
  }

}
