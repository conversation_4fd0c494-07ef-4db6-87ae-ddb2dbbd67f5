server:
  port: @server.port@
  servlet:
    context-path: /${spring.application.name}
    encoding:
      enabled: true
      force: true
      charset: utf-8

spring:
  application:
    name: @app.name@
  profiles:
    active: @active.profile@
  mvc:
    static-path-pattern: /resources/**
  shardingsphere:
    datasource:
      names: galaxy-ds
      galaxy-ds:
        url: @jdbc.url.0@
        username: @jdbc.username.0@
        password: @jdbc.password.0@
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
    sharding:
      tables:
        gxy_session:
          actual-data-nodes: galaxy-ds.gxy_session_$->{2025..2026}0$->{1..9},galaxy-ds.gxy_session_$->{2025..2026}$->{10..12}
          table-strategy:
            standard:
              sharding-column: create_time
              precise-algorithm-class-name: com.ximalaya.galaxy.business.repo.algorithm.GalaxySessionYearMonthPreciseShardingAlgorithm
              range-algorithm-class-name: com.ximalaya.galaxy.business.repo.algorithm.GalaxySessionYearMonthRangeShardingAlgorithm
          key-generator:
            type: SNOWFLAKE
            column: session_code
        gxy_phase:
          actual-data-nodes: galaxy-ds.gxy_phase_$->{0..19}
          table-strategy:
            standard:
              sharding-column: session_code
              precise-algorithm-class-name: com.ximalaya.galaxy.business.repo.algorithm.GalaxyPhaseShardingAlgorithm
          key-generator:
            type: SNOWFLAKE
            column: phase_code
        gxy_block:
          actual-data-nodes: galaxy-ds.gxy_block_$->{0..19}
          table-strategy:
            standard:
              sharding-column: session_code
              precise-algorithm-class-name: com.ximalaya.galaxy.business.repo.algorithm.GalaxyBlockShardingAlgorithm
          key-generator:
            type: SNOWFLAKE
            column: block_code
  # freemarker模板引擎
  freemarker:
    template-loader-path:
      - classpath:/templates/
    cache: false
    charset: UTF-8
    content-type: text/html
    suffix: .ftlh
    allow-request-override: true
    allow-session-override: true
    expose-request-attributes: true
    expose-spring-macro-helpers: true
  # redis
  redis:
    database: @redis.database@
    host: @redis.host@
    port: @redis.port@
    password: @redis.password@
    # 链接超时时间 单位 ms（毫秒）
    timeout: 10000ms
    lettuce:
      pool:
        # 连接池最大连接数（使用负值表示没有限制） 默认 8
        max-active: 8
        # 连接池最大阻塞等待时间（使用负值表示没有限制） 默认 -1
        max-wait: -1
        # 连接池中的最大空闲连接 默认 8
        max-idle: 8
        # 连接池中的最小空闲连接 默认 0
        min-idle: 0

mainstay:
  application:
    name: ${spring.application.name}
    level: 2
    owners:
      - name: shengzhe.zhang
        email: <EMAIL>
  registry:
    provider: @mainstay.registry.provider@
  server:
    galaxy-business-worker:
      group: @mainstay.server.group@           # required if spring 2.x, spring 2.x 由于约定了yaml上的key不支持camel体，如果用的2的话请填写该group，默认以此group优先
      port: @mainstay.server.thrift.port@                # required rpc 端口
      workerSize: @mainstay.server.workSize@           # optional rpc worker 线程池线程数
      queueSize: @mainstay.server.queueSize@            # optional rpc worker 线程池队列数
      ioSize: @mainstay.server.ioSize@                  # optional netty io 线程数
      maxConnectionSize: @mainstay.server.maxConnectionSize@
      acceptorSize: @mainstay.server.maxAcquirePendingSize@
  protocol:
    codec: thrift                # optional

eros:
  xdcs:
    healthCheckUrl: /healthcheck
    logDir: /var/log
    appEventType: other
    appName: @app.name@
    envId: 1
  log:
    path: /var/log/galaxy-business-worker
  football:
    app:
      id: 15306
      name: galaxy-business-worker
  danube:
    rocket-producers:
      - groupName: pg_galaxy_business_worker_topic_galaxy_realtime_message

# mybatis-plus
mybatis-plus:
  globalConfig:
    dbConfig:
      idType: AUTO
      tablePrefix: galaxy_
  mapper-locations: classpath:mapper/*.xml

galaxy:
  security-key: 9680e7dfdd8d933554ac3e2067bd2210c1c4cc28dd6b0fb7bf8d1e01a49dfdb4
  agent:
    path: http://************:12897/galaxy-host