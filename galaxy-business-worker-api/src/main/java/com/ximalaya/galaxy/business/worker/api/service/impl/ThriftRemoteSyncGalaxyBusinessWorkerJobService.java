/**
 * Generated by Dalaran
 *   version: 0.0.61
 */
package com.ximalaya.galaxy.business.worker.api.service.impl;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.lang.RuntimeException;

import org.springframework.beans.factory.annotation.Autowired;

import com.ximalaya.mainstay.rpc.thrift.TException;

import com.ximalaya.galaxy.business.worker.api.service.*;
import com.ximalaya.galaxy.business.worker.api.ex.*;


public class ThriftRemoteSyncGalaxyBusinessWorkerJobService implements IRemoteSyncGalaxyBusinessWorkerJobService {

  @Autowired
  private com.ximalaya.galaxy.business.worker.api.thrift.GalaxyBusinessWorkerJobService.Iface client;

  
  @Override
  public com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse startSessionJob(com.ximalaya.galaxy.business.worker.api.model.StartSessionJobRequest startRequest) {
    try {
      com.ximalaya.galaxy.business.worker.api.thrift.entity.StartSessionJobRequest _startRequest_arg_item = com.ximalaya.galaxy.business.worker.api.converter.StartSessionJobRequestConverter.transform(startRequest);
  
      com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse client_result = client.startSessionJob(_startRequest_arg_item);
      if (client_result == null) {
        return null;
      }
  
      com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse _client_result_result_item = com.ximalaya.galaxy.business.worker.api.converter.CommonJobResponseConverter.transform(client_result);
      return _client_result_result_item;
    } catch (TException e) {
      throw new GalaxyBusinessWorkerJobServiceException("call startSessionJob failed.", e);
    }
  }
  
  @Override
  public com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse startToolJob(com.ximalaya.galaxy.business.worker.api.model.StartToolJobRequest startRequest) {
    try {
      com.ximalaya.galaxy.business.worker.api.thrift.entity.StartToolJobRequest _startRequest_arg_item = com.ximalaya.galaxy.business.worker.api.converter.StartToolJobRequestConverter.transform(startRequest);
  
      com.ximalaya.galaxy.business.worker.api.thrift.entity.CommonJobResponse client_result = client.startToolJob(_startRequest_arg_item);
      if (client_result == null) {
        return null;
      }
  
      com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse _client_result_result_item = com.ximalaya.galaxy.business.worker.api.converter.CommonJobResponseConverter.transform(client_result);
      return _client_result_result_item;
    } catch (TException e) {
      throw new GalaxyBusinessWorkerJobServiceException("call startToolJob failed.", e);
    }
  }
}