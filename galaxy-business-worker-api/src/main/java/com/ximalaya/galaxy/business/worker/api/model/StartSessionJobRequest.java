/**
 * Generated by Dal<PERSON>
 *   version: 0.0.61
 */
package com.ximalaya.galaxy.business.worker.api.model;

import java.nio.ByteBuffer;
import java.util.*;


public class StartSessionJobRequest {

  private Long sessionCode;
  private Long phaseCode;
  private String systemPrompt;
  private String prompt;

  public StartSessionJobRequest() {
  }

  public StartSessionJobRequest(Long sessionCode, Long phaseCode, String systemPrompt, String prompt) {
    this.sessionCode = sessionCode;
    this.phaseCode = phaseCode;
    this.systemPrompt = systemPrompt;
    this.prompt = prompt;
  }

  public void setSessionCode(Long sessionCode) {
    this.sessionCode = sessionCode;
  }

  public Long getSessionCode() {
    return this.sessionCode;
  }

  public void setPhaseCode(Long phaseCode) {
    this.phaseCode = phaseCode;
  }

  public Long getPhaseCode() {
    return this.phaseCode;
  }

  public void setSystemPrompt(String systemPrompt) {
    this.systemPrompt = systemPrompt;
  }

  public String getSystemPrompt() {
    return this.systemPrompt;
  }

  public void setPrompt(String prompt) {
    this.prompt = prompt;
  }

  public String getPrompt() {
    return this.prompt;
  }

  @Override
  public String toString() {
    return "StartSessionJobRequest(" +"sessionCode=" + this.sessionCode + ", " + "phaseCode=" + this.phaseCode + ", " + "systemPrompt=" + this.systemPrompt + ", " + "prompt=" + this.prompt + ")";
  }

  @Override
  public int hashCode() {
    int hash = 1;
    hash = 31 * hash + (this.sessionCode == null ? 0 : this.sessionCode.hashCode());
    hash = 31 * hash + (this.phaseCode == null ? 0 : this.phaseCode.hashCode());
    hash = 31 * hash + (this.systemPrompt == null ? 0 : this.systemPrompt.hashCode());
    hash = 31 * hash + (this.prompt == null ? 0 : this.prompt.hashCode());
    return hash;
  }

  @Override
  public boolean equals(Object other) {
    if (!(other instanceof StartSessionJobRequest)) {
      return false;
    }
    StartSessionJobRequest that = (StartSessionJobRequest) other;
    if (this.sessionCode == null) {
      if (that.sessionCode != null)
        return false;
    } else if (!this.sessionCode.equals(that.sessionCode)) {
      return false;
    }
    if (this.phaseCode == null) {
      if (that.phaseCode != null)
        return false;
    } else if (!this.phaseCode.equals(that.phaseCode)) {
      return false;
    }
    if (this.systemPrompt == null) {
      if (that.systemPrompt != null)
        return false;
    } else if (!this.systemPrompt.equals(that.systemPrompt)) {
      return false;
    }
    if (this.prompt == null) {
      if (that.prompt != null)
        return false;
    } else if (!this.prompt.equals(that.prompt)) {
      return false;
    }
    return true;
  }
}