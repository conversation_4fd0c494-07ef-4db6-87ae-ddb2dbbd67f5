/**
 * Generated by Dal<PERSON>
 *   version: 0.0.61
 */
package com.ximalaya.galaxy.business.worker.api.converter;

import java.nio.ByteBuffer;
import java.util.*;


public class StartToolJobRequestConverter {

  public static com.ximalaya.galaxy.business.worker.api.model.StartToolJobRequest transform(com.ximalaya.galaxy.business.worker.api.thrift.entity.StartToolJobRequest source) {
    com.ximalaya.galaxy.business.worker.api.model.StartToolJobRequest target = new com.ximalaya.galaxy.business.worker.api.model.StartToolJobRequest();
    if (null != source) {
      if (source.isSetSessionCode()) {
        Long _sessionCode_item = source.getSessionCode();
        target.setSessionCode(_sessionCode_item);
      }
      if (source.isSetPhaseCode()) {
        Long _phaseCode_item = source.getPhaseCode();
        target.setPhaseCode(_phaseCode_item);
      }
      if (source.isSetToolId()) {
        Long _toolId_item = source.getToolId();
        target.setToolId(_toolId_item);
      }
      if (source.isSetArgs()) {
        String _args_item = source.getArgs();
        target.setArgs(_args_item);
      }
      if (source.isSetNeedRemoveBlockCodes()) {
      List<Long> _needRemoveBlockCodes_item = new ArrayList<Long>(source.getNeedRemoveBlockCodes().size());
      
      for (Long needRemoveBlockCodes_item_element : source.getNeedRemoveBlockCodes()) {
        Long _needRemoveBlockCodes_item_element = needRemoveBlockCodes_item_element;
        _needRemoveBlockCodes_item.add(_needRemoveBlockCodes_item_element);
      }
        target.setNeedRemoveBlockCodes(_needRemoveBlockCodes_item);
      }
    }
    return target;
  }

  public static com.ximalaya.galaxy.business.worker.api.thrift.entity.StartToolJobRequest transform(com.ximalaya.galaxy.business.worker.api.model.StartToolJobRequest source) {
    com.ximalaya.galaxy.business.worker.api.thrift.entity.StartToolJobRequest.Builder target = new com.ximalaya.galaxy.business.worker.api.thrift.entity.StartToolJobRequest.Builder();
    if (null != source) {
      if (source.getSessionCode() != null) {
        Long _sessionCode_item = source.getSessionCode();
        target.setSessionCode(_sessionCode_item);
      }
      if (source.getPhaseCode() != null) {
        Long _phaseCode_item = source.getPhaseCode();
        target.setPhaseCode(_phaseCode_item);
      }
      if (source.getToolId() != null) {
        Long _toolId_item = source.getToolId();
        target.setToolId(_toolId_item);
      }
      if (source.getArgs() != null) {
        String _args_item = source.getArgs();
        target.setArgs(_args_item);
      }
      if (source.getNeedRemoveBlockCodes() != null) {
      List<Long> _needRemoveBlockCodes_item = new ArrayList<Long>(source.getNeedRemoveBlockCodes().size());
      
      for (Long needRemoveBlockCodes_item_element : source.getNeedRemoveBlockCodes()) {
        Long _needRemoveBlockCodes_item_element = needRemoveBlockCodes_item_element;
        _needRemoveBlockCodes_item.add(_needRemoveBlockCodes_item_element);
      }
        target.setNeedRemoveBlockCodes(_needRemoveBlockCodes_item);
      }
 
    }
    return target.build();
  }
}