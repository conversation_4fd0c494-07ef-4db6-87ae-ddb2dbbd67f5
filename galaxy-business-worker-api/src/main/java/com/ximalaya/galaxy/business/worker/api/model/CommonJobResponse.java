/**
 * Generated by Dalaran
 *   version: 0.0.61
 */
package com.ximalaya.galaxy.business.worker.api.model;

import java.nio.ByteBuffer;
import java.util.*;


public class CommonJobResponse {

  /** 结果状态 200-成功 other-失败*/
  private Integer code;
  /** 结果消息 */
  private String message;

  public CommonJobResponse() {
  }

  public CommonJobResponse(Integer code, String message) {
    this.code = code;
    this.message = message;
  }

  public void setCode(Integer code) {
    this.code = code;
  }

  public Integer getCode() {
    return this.code;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public String getMessage() {
    return this.message;
  }

  @Override
  public String toString() {
    return "CommonJobResponse(" +"code=" + this.code + ", " + "message=" + this.message + ")";
  }

  @Override
  public int hashCode() {
    int hash = 1;
    hash = 31 * hash + (this.code == null ? 0 : this.code.hashCode());
    hash = 31 * hash + (this.message == null ? 0 : this.message.hashCode());
    return hash;
  }

  @Override
  public boolean equals(Object other) {
    if (!(other instanceof CommonJobResponse)) {
      return false;
    }
    CommonJobResponse that = (CommonJobResponse) other;
    if (this.code == null) {
      if (that.code != null)
        return false;
    } else if (!this.code.equals(that.code)) {
      return false;
    }
    if (this.message == null) {
      if (that.message != null)
        return false;
    } else if (!this.message.equals(that.message)) {
      return false;
    }
    return true;
  }
}