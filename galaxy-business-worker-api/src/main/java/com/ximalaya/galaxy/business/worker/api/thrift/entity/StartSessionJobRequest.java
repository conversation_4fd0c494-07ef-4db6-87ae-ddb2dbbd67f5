/**
 * Generated by <PERSON><PERSON>
 *   version: 0.0.61
 */
package com.ximalaya.galaxy.business.worker.api.thrift.entity;

import java.nio.ByteBuffer;
import java.util.*;

import com.ximalaya.mainstay.common.MainstayCodecException;
import com.ximalaya.mainstay.common.Option;
import com.ximalaya.mainstay.common.RouteArg;
import com.ximalaya.mainstay.common.util.Utilities;
import com.ximalaya.mainstay.rpc.thrift.ThriftStruct;
import com.ximalaya.mainstay.rpc.thrift.ThriftStructCodec;
import com.ximalaya.mainstay.rpc.thrift.TException;
import com.ximalaya.mainstay.rpc.thrift.protocol.*;


public class StartSessionJobRequest implements ThriftStruct {
  private static final TStruct STRUCT = new TStruct("StartSessionJobRequest");
  private static final TField SessionCodeField = new TField("sessionCode", TType.I64, (short) 1);
  final long sessionCode;
  boolean isSetSessionCode;
  private static final TField PhaseCodeField = new TField("phaseCode", TType.I64, (short) 2);
  final long phaseCode;
  boolean isSetPhaseCode;
  private static final TField SystemPromptField = new TField("systemPrompt", TType.STRING, (short) 3);
  final String systemPrompt;
  boolean isSetSystemPrompt;
  private static final TField PromptField = new TField("prompt", TType.STRING, (short) 4);
  final String prompt;
  boolean isSetPrompt;

  public static class Builder {
    private long _sessionCode = 0L;
    private boolean isSetSessionCode = false;
    public Builder setSessionCode(long value) {
      this._sessionCode = value;
        this.isSetSessionCode = true;
      return this;
    }


    public boolean isSetSessionCode() {
      return isSetSessionCode;
    }

    public long getSessionCode() {
      return this._sessionCode;
    }

    public Builder unsetSessionCode() {
      this._sessionCode = 0L;
      return this;
    }

    // 仅用于发送空字段到服务端
    public Builder emptySessionCode() {
      this._sessionCode = 0L;
      this.isSetSessionCode = true;
      return this;
    }

    private long _phaseCode = 0L;
    private boolean isSetPhaseCode = false;
    public Builder setPhaseCode(long value) {
      this._phaseCode = value;
        this.isSetPhaseCode = true;
      return this;
    }


    public boolean isSetPhaseCode() {
      return isSetPhaseCode;
    }

    public long getPhaseCode() {
      return this._phaseCode;
    }

    public Builder unsetPhaseCode() {
      this._phaseCode = 0L;
      return this;
    }

    // 仅用于发送空字段到服务端
    public Builder emptyPhaseCode() {
      this._phaseCode = 0L;
      this.isSetPhaseCode = true;
      return this;
    }

    private String _systemPrompt = null;
    private boolean isSetSystemPrompt = false;
    public Builder setSystemPrompt(String value) {
      this._systemPrompt = value;
      if (value != null) {
        this.isSetSystemPrompt = true;
      }
      return this;
    }


    public boolean isSetSystemPrompt() {
      return isSetSystemPrompt;
    }

    public String getSystemPrompt() {
      return this._systemPrompt;
    }

    public Builder unsetSystemPrompt() {
      this._systemPrompt = null;
      return this;
    }

    // 仅用于发送空字段到服务端
    public Builder emptySystemPrompt() {
      this._systemPrompt = null;
      this.isSetSystemPrompt = true;
      return this;
    }

    private String _prompt = null;
    private boolean isSetPrompt = false;
    public Builder setPrompt(String value) {
      this._prompt = value;
      if (value != null) {
        this.isSetPrompt = true;
      }
      return this;
    }


    public boolean isSetPrompt() {
      return isSetPrompt;
    }

    public String getPrompt() {
      return this._prompt;
    }

    public Builder unsetPrompt() {
      this._prompt = null;
      return this;
    }

    // 仅用于发送空字段到服务端
    public Builder emptyPrompt() {
      this._prompt = null;
      this.isSetPrompt = true;
      return this;
    }


    public StartSessionJobRequest build() {
      StartSessionJobRequest model = new StartSessionJobRequest(this._sessionCode, this._phaseCode, this._systemPrompt, this._prompt);
      model.isSetSessionCode = isSetSessionCode;
      model.isSetPhaseCode = isSetPhaseCode;
      model.isSetSystemPrompt = isSetSystemPrompt;
      model.isSetPrompt = isSetPrompt;
      return model;
    }
  }

  public Builder copy() {
    Builder builder = new Builder();
    if (isSetSessionCode) builder.setSessionCode(this.sessionCode);
    if (isSetPhaseCode) builder.setPhaseCode(this.phaseCode);
    if (isSetSystemPrompt) builder.setSystemPrompt(this.systemPrompt);
    if (isSetPrompt) builder.setPrompt(this.prompt);
    return builder;
  }

  public static final ThriftStructCodec<StartSessionJobRequest> CODEC = new ThriftStructCodec<StartSessionJobRequest>() {
    @Override
    public StartSessionJobRequest decode(TProtocol _iprot) throws TException {
      Builder builder = new Builder();
      long sessionCode = 0L;
      long phaseCode = 0L;
      String systemPrompt = null;
      String prompt = null;
      Boolean _done = false;
      _iprot.readStructBegin();
      while (!_done) {
        TField _field = _iprot.readFieldBegin();
        if (_field.type == TType.STOP) {
          _done = true;
        } else {
          switch (_field.id) {
            case 1: /* sessionCode */
              switch (_field.type) {
                case TType.I64:
                  Long sessionCode_item;
                  sessionCode_item = _iprot.readI64();
                  sessionCode = sessionCode_item;
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              builder.setSessionCode(sessionCode);
              break;
            case 2: /* phaseCode */
              switch (_field.type) {
                case TType.I64:
                  Long phaseCode_item;
                  phaseCode_item = _iprot.readI64();
                  phaseCode = phaseCode_item;
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              builder.setPhaseCode(phaseCode);
              break;
            case 3: /* systemPrompt */
              switch (_field.type) {
                case TType.STRING:
                  String systemPrompt_item;
                  systemPrompt_item = _iprot.readString();
                  systemPrompt = systemPrompt_item;
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              builder.setSystemPrompt(systemPrompt);
              break;
            case 4: /* prompt */
              switch (_field.type) {
                case TType.STRING:
                  String prompt_item;
                  prompt_item = _iprot.readString();
                  prompt = prompt_item;
                  break;
                default:
                  TProtocolUtil.skip(_iprot, _field.type);
              }
              builder.setPrompt(prompt);
              break;
            default:
              TProtocolUtil.skip(_iprot, _field.type);
          }
          _iprot.readFieldEnd();
        }
      }
      _iprot.readStructEnd();
      try {
        return builder.build();
      } catch (IllegalStateException stateEx) {
        throw new TProtocolException(stateEx.getMessage());
      }
    }

    @Override
    public void encode(StartSessionJobRequest struct, TProtocol oprot) throws TException {
      struct.write(oprot);
    }
  };

  public static StartSessionJobRequest decode(TProtocol _iprot) throws TException {
    return CODEC.decode(_iprot);
  }

  public static void encode(StartSessionJobRequest struct, TProtocol oprot) throws TException {
    CODEC.encode(struct, oprot);
  }

  StartSessionJobRequest(long sessionCode, long phaseCode, String systemPrompt, String prompt) {
    this.sessionCode = sessionCode;
    this.phaseCode = phaseCode;
    this.systemPrompt = systemPrompt;
    this.prompt = prompt;
  }


  public long getSessionCode() {
    return this.sessionCode;
  }
  
  public boolean isSetSessionCode() {
    return this.isSetSessionCode;
  }
  public long getPhaseCode() {
    return this.phaseCode;
  }
  
  public boolean isSetPhaseCode() {
    return this.isSetPhaseCode;
  }
  public String getSystemPrompt() {
    return this.systemPrompt;
  }
  
  public boolean isSetSystemPrompt() {
    return this.isSetSystemPrompt;
  }
  public String getPrompt() {
    return this.prompt;
  }
  
  public boolean isSetPrompt() {
    return this.isSetPrompt;
  }

  @Override
  public void write(TProtocol _oprot) throws TException {
    validate();
    _oprot.writeStructBegin(STRUCT);
    if (this.isSetSessionCode) {
        _oprot.writeFieldBegin(SessionCodeField);
        Long sessionCode_item = sessionCode;
        _oprot.writeI64(sessionCode_item);
        _oprot.writeFieldEnd();
    }
    if (this.isSetPhaseCode) {
        _oprot.writeFieldBegin(PhaseCodeField);
        Long phaseCode_item = phaseCode;
        _oprot.writeI64(phaseCode_item);
        _oprot.writeFieldEnd();
    }
    if (this.isSetSystemPrompt) {
      if (systemPrompt != null) {
        _oprot.writeFieldBegin(SystemPromptField);
        String systemPrompt_item = systemPrompt;
        _oprot.writeString(systemPrompt_item);
        _oprot.writeFieldEnd();
      }
    }
    if (this.isSetPrompt) {
      if (prompt != null) {
        _oprot.writeFieldBegin(PromptField);
        String prompt_item = prompt;
        _oprot.writeString(prompt_item);
        _oprot.writeFieldEnd();
      }
    }
    _oprot.writeFieldStop();
    _oprot.writeStructEnd();
  }

  private void validate() throws TProtocolException {
  }


  @Override
  public boolean equals(Object other) {
    if (!(other instanceof StartSessionJobRequest)) {
      return false;
    }
    StartSessionJobRequest that = (StartSessionJobRequest) other;
    if (this.sessionCode != that.sessionCode) {
      return false;
    }
    if (this.phaseCode != that.phaseCode) {
      return false;
    }
    if (this.systemPrompt == null) {
      if (that.systemPrompt != null)
        return false;
    } else if (!this.systemPrompt.equals(that.systemPrompt)) {
      return false;
    }
    if (this.prompt == null) {
      if (that.prompt != null)
        return false;
    } else if (!this.prompt.equals(that.prompt)) {
      return false;
    }
    return true;
  }

  private int hash(Object... values) {
    return java.util.Arrays.hashCode(values);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("StartSessionJobRequest(");
    if (this.isSetSessionCode) {
      sb.append("sessionCode=");
      sb.append(this.sessionCode);
      sb.append(", ");
    }
    if (this.isSetPhaseCode) {
      sb.append("phaseCode=");
      sb.append(this.phaseCode);
      sb.append(", ");
    }
    if (this.isSetSystemPrompt) {
      sb.append("systemPrompt=");
      sb.append(this.systemPrompt);
      sb.append(", ");
    }
    if (this.isSetPrompt) {
      sb.append("prompt=");
      sb.append(this.prompt);
      sb.append(", ");
    }
    sb.append(")");
    return sb.toString();
  }

  @Override
  public int hashCode() {
    int hash = 1;
    hash = 31 * hash + hash * hash(this.sessionCode);
    hash = 31 * hash + hash * hash(this.phaseCode);
    hash = 31 * hash + hash * (this.systemPrompt == null ? 0 : this.systemPrompt.hashCode());
    hash = 31 * hash + hash * (this.prompt == null ? 0 : this.prompt.hashCode());
    return hash;
  }
}