package com.ximalaya.galaxy.business.business.boss.configuration;

import com.ximalaya.eros.mainstay.context.annotation.MainstayClient;
import com.ximalaya.galaxy.business.worker.api.thrift.GalaxyBusinessWorkerJobService;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @create 2025-05-20 16:33
 */
@Configuration
public class BossRpcConsumerConfiguration {

  @MainstayClient(group = "galaxy-business-worker", autoRegisterJavaIface = true, timeout = "10000")
  private GalaxyBusinessWorkerJobService.Iface galaxyBusinessWorkerJobService;

}
