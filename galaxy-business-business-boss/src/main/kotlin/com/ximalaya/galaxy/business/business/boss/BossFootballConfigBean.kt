package com.ximalaya.galaxy.business.business.boss

import com.ximalaya.football.client.spring.annotation.FootballConfig
import com.ximalaya.football.client.spring.annotation.FootballField
import com.ximalaya.galaxy.business.common.support.SplitHelper
import org.springframework.stereotype.Component

/**
 *<AUTHOR>
 *@create 2025-05-06 17:24
 */
@FootballConfig("common-config")
@Component
class BossFootballConfigBean {

    @FootballField(name = "hello.world", defaultValue = "")
    var helloFootball: String? = null

    @FootballField(name = "galaxy.admins", defaultValue = "")
    var galaxyAdmins: String? = null

    fun getAdmins(): List<Long> {
        return SplitHelper.deserialize(galaxyAdmins) { java.lang.Long.valueOf(it) }
    }

    fun isAdmin(uid: Long): Boolean {
        return getAdmins().contains(uid)
    }

    @FootballField(name = "user.inner", defaultValue = "")
    var innerUsers: String? = null

    fun getInnerUsers(): List<Long> {
        return SplitHelper.deserialize(innerUsers) { java.lang.Long.valueOf(it) }
    }

    fun isInnerUser(uid: Long): Boolean {
        return getInnerUsers().contains(uid)
    }

}