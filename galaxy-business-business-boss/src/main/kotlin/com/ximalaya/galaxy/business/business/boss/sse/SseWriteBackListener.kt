package com.ximalaya.galaxy.business.business.boss.sse

import com.ximalaya.galaxy.business.business.boss.BizGalaxyBoss
import com.ximalaya.galaxy.business.service.vo.AgentContentProtocol
import org.redisson.api.RTopic
import org.redisson.api.RedissonClient
import org.springframework.beans.factory.InitializingBean
import org.springframework.stereotype.Component

/**
 *<AUTHOR>
 *@create 2025-05-22 00:00
 */
@Component
class SseWriteBackListener(
  private val redissonClient: RedissonClient,
  private val bizGalaxyBoss: BizGalaxyBoss,
) : InitializingBean {

  private var writeBackTopic: RTopic? = null

  override fun afterPropertiesSet() {
    writeBackTopic = redissonClient.getTopic("sse-write-back-topic")

    writeBackTopic!!.addListener(String::class.java, { _, msg ->
      val agentMessage = AgentContentProtocol.parseJson(msg.toString())

      bizGalaxyBoss.sendSseWriteBackMessage(agentMessage)
    })
  }

}