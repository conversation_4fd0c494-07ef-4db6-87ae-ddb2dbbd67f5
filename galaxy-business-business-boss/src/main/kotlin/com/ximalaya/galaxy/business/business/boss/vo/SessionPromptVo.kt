package com.ximalaya.galaxy.business.business.boss.vo

import io.swagger.annotations.ApiModel
import io.swagger.annotations.ApiModelProperty
import javax.validation.constraints.NotBlank

/**
 *<AUTHOR>
 *@create 2025-05-25 23:44
 */
@ApiModel(description = "会话Prompt VO")
class SessionPromptVo {

  @ApiModelProperty("系统prompt名称")
  @NotBlank(message = "系统prompt不能为空哦~")
  var systemPromptName: String? = null

  @ApiModelProperty("prompt")
  @NotBlank(message = "prompt不能为空哦~")
  var prompt: String? = null

}