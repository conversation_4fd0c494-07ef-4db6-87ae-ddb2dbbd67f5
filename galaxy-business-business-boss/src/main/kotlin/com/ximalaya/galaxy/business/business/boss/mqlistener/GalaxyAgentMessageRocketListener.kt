package com.ximalaya.galaxy.business.business.boss.mqlistener

import com.ximalaya.danube.core.consumer.rocket.RocketListener
import com.ximalaya.danube.core.model.DanubeMessage
import com.ximalaya.galaxy.business.business.boss.BizGalaxyBoss
import com.ximalaya.galaxy.business.business.boss.sse.SseWriteBackSender
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.support.GalaxyAsserts
import com.ximalaya.galaxy.business.repo.enums.BlockType
import com.ximalaya.galaxy.business.repo.enums.PhaseState
import com.ximalaya.galaxy.business.repo.service.GalaxyBlockService
import com.ximalaya.galaxy.business.service.enums.AgentContentType
import com.ximalaya.galaxy.business.service.support.RedisLockHelper
import com.ximalaya.galaxy.business.service.support.getAgentMessageCacheKey
import com.ximalaya.galaxy.business.service.vo.AgentContentProtocol
import com.ximalaya.galaxy.business.service.vo.AgentContentText
import mu.KotlinLogging
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.stereotype.Component
import java.nio.charset.StandardCharsets

/**
 *<AUTHOR>
 *@create 2025-05-21 23:34
 */
@Component("galaxyAgentMessageRocketListener")
class GalaxyAgentMessageRocketListener(
    private val bizGalaxyBoss: BizGalaxyBoss,
    private val stringRedisTemplate: StringRedisTemplate,
    private val sseWriteBackSender: SseWriteBackSender,
    private val galaxyBlockService: GalaxyBlockService,
) : RocketListener {

    override fun onMessage(danubeMessage: DanubeMessage) {
        val tag = danubeMessage.tag

        val keys = danubeMessage.keys

        /**
         * @see com.ximalaya.galaxy.business.service.vo.AgentContentProtocol
         */
        val data = String(danubeMessage.value, StandardCharsets.UTF_8)

        logger.info("Receive Rocket Topic: Agent消息 tag: {}, keys: {}, message: {}", tag, keys, data)

        try {
            val (sessionCode, phaseCode) = decodeRocketKey(keys)
            val agentMessage = AgentContentProtocol.parseJson(data)

            agentMessage.sessionCode = sessionCode
            agentMessage.phaseCode = phaseCode

            // 处理stop消息 其余消息直接传递出去
            when (agentMessage._type) {
                AgentContentType.FINISH, AgentContentType.ERROR, AgentContentType.EXCEPTION -> {
                    // 更新阶段状态
                    if (agentMessage._type == AgentContentType.FINISH) {
                        bizGalaxyBoss.updatePhaseState(sessionCode, phaseCode, PhaseState.FINISHED)
                    } else {
                        bizGalaxyBoss.updatePhaseState(sessionCode, phaseCode, PhaseState.FAILED)
                    }

                    // 从Redis List获取该阶段的所有消息
                    val messageRedisKey = getAgentMessageCacheKey(sessionCode, phaseCode)
                    val listSize = stringRedisTemplate.opsForList().size(messageRedisKey) ?: 0

                    if (listSize > 0) {
                        // 获取Redis list中的所有消息
                        val messageJsons = stringRedisTemplate.opsForList().range(messageRedisKey, 0, listSize - 1)

                        if (messageJsons.isNullOrEmpty()) {
                            return
                        }

                        val messageVos = messageJsons.map {
                            AgentContentProtocol.parseJson(it)
                        }

                        val blockCodeMessagesMapping = messageVos.groupBy { it.blockCode }

                        for (codeMessages in blockCodeMessagesMapping.entries) {
                            if (codeMessages.key == null) {
                                for (exceptionMessage in codeMessages.value) {
                                    galaxyBlockService.createBlock(
                                        sessionCode,
                                        phaseCode,
                                        BlockType.EXCEPTION,
                                        exceptionMessage.toJson()
                                    )
                                }

                                continue
                            }
                            val blockCode = codeMessages.key!!
                            val messages = codeMessages.value.filter {
                                !setOf(
                                    AgentContentType.BLOCK_USER,
                                    AgentContentType.BLOCK_TOOL_CALL,
                                    AgentContentType.BLOCK_TOOL_RESULT,
                                    AgentContentType.BLOCK_DIFY_CALL,
                                    AgentContentType.BLOCK_DIFY_RESULT
                                ).contains(it._type)
                            }.sortedBy { it.index }

                            var content = ""
                            for (message in messages) {
                                when (message._type) {
                                    AgentContentType.TEXT -> {
                                        content += (message as AgentContentText).text
                                    }

                                    AgentContentType.TEXT_DELTA -> {
                                        content += (message as AgentContentText).text
                                    }

                                    else -> {
                                        content = message.toJson()
                                    }
                                }
                            }

                            galaxyBlockService.updateBlock(
                                sessionCode,
                                phaseCode,
                                blockCode,
                                content
                            )

                            logger.info(
                                "持久化消息到MySQL, sessionCode: {}, phaseCode: {}, blockCode: {}",
                                sessionCode, phaseCode, blockCode
                            )
                        }

                        // 清空Redis缓存
                        stringRedisTemplate.delete(messageRedisKey)
                    }

                    // 任务完成或失败，删除锁
                    stringRedisTemplate.delete(RedisLockHelper.getPhaseOngoingLockKey(sessionCode, phaseCode))

                    sseWriteBackSender.sendMessage(agentMessage)
                }

                else -> {
                    sseWriteBackSender.sendMessage(agentMessage)
                }
            }
        } catch (e: Exception) {
            // fixme 删除 try catch
            logger.error("发生错误", e)
        }

    }

    private fun decodeRocketKey(encodedKey: String): Pair<Long, Long> {
        val parts = encodedKey.split("-", limit = 2)
        GalaxyAsserts.assertTrue(parts.size == 2, ErrorCode.SESSION_ERROR, "Invalid Rocket key format: $encodedKey")
        return Pair(parts[0].toLong(), parts[1].toLong())
    }

    companion object {
        private val logger = KotlinLogging.logger { }
    }

}