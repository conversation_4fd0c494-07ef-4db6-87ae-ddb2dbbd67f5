package com.ximalaya.galaxy.business.business.boss.vo

import com.alibaba.fastjson.annotation.JSONField
import com.ximalaya.galaxy.business.worker.api.thrift.entity.StartSessionJobRequest

/**
 *<AUTHOR>
 *@create 2025-05-20 16:02
 */
data class StartSessionJobVo(

  var sessionCode: Long? = null,

  var phaseCode: Long? = null,

  var systemPrompt: String? = null,

  var prompt: String? = null,
) {

  @JSONField(serialize = false, deserialize = false)
  fun toStartSessionJobRequest(): StartSessionJobRequest {
    return StartSessionJobRequest.Builder()
      .setSessionCode(this.sessionCode!!)
      .setPhaseCode(this.phaseCode!!)
      .setSystemPrompt(this.systemPrompt)
      .setPrompt(this.prompt)
      .build()
  }

}