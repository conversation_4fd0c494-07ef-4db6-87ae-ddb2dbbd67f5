package com.ximalaya.galaxy.business.business.boss.auth;

import com.ximalaya.common.auth.common.subject.UserContext;
import com.ximalaya.galaxy.business.business.boss.BossFootballConfigBean;
import com.ximalaya.galaxy.business.common.enums.ErrorCode;
import com.ximalaya.galaxy.business.common.exception.GalaxyException;
import com.ximalaya.galaxy.business.common.exception.GalaxyNoAuthException;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2023-03-10 16:50
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class AdminLimitAspect {

  private final BossFootballConfigBean footballConfig;

  @Pointcut("@annotation(AdminLimit)")
  public void pointcut() {
  }

  @Around(value = "pointcut() && @annotation(AdminLimit)")
  public Object process(ProceedingJoinPoint joinPoint) throws Throwable {
    Long uid = Optional.ofNullable(UserContext.getUid())
        .orElseThrow(() -> new GalaxyException(ErrorCode.NOT_LOGIN_ERROR));
    if (!footballConfig.isAdmin(uid)) {
      throw new GalaxyNoAuthException();
    }
    return joinPoint.proceed();
  }

}
