package com.ximalaya.galaxy.business.business.boss.sse

import com.ximalaya.galaxy.business.service.vo.AgentContentProtocol
import org.redisson.api.RTopic
import org.redisson.api.RedissonClient
import org.springframework.beans.factory.InitializingBean
import org.springframework.stereotype.Component

/**
 *<AUTHOR>
 *@create 2025-05-22 00:00
 */
@Component
class SseWriteBackSender(
  private val redissonClient: RedissonClient,
) : InitializingBean {

  private var writeBackTopic: RTopic? = null

  fun sendMessage(agentMessage: AgentContentProtocol) {
    writeBackTopic!!.publish(agentMessage.toJson())
  }

  override fun afterPropertiesSet() {
    writeBackTopic = redissonClient.getTopic("sse-write-back-topic")
  }

}