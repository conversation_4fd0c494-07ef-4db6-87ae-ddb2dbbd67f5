package com.ximalaya.galaxy.business.business.boss.impl

import com.ximalaya.galaxy.business.business.BusinessComponent
import com.ximalaya.galaxy.business.business.boss.BizGalaxyBoss
import com.ximalaya.galaxy.business.business.boss.sse.SseWriteBackSender
import com.ximalaya.galaxy.business.business.boss.vo.SessionVo
import com.ximalaya.galaxy.business.business.boss.vo.StartSessionJobVo
import com.ximalaya.galaxy.business.common.ALBUM_OUTLINE
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.support.GalaxyAsserts
import com.ximalaya.galaxy.business.repo.entity.GalaxyBlockEntity
import com.ximalaya.galaxy.business.repo.entity.GalaxyPhaseEntity
import com.ximalaya.galaxy.business.repo.entity.GalaxySessionEntity
import com.ximalaya.galaxy.business.repo.enums.BlockType
import com.ximalaya.galaxy.business.repo.enums.LogicDeleted
import com.ximalaya.galaxy.business.repo.enums.PhaseState
import com.ximalaya.galaxy.business.repo.enums.SessionState
import com.ximalaya.galaxy.business.repo.service.GalaxyBlockService
import com.ximalaya.galaxy.business.repo.service.GalaxyPhaseService
import com.ximalaya.galaxy.business.repo.service.GalaxySessionService
import com.ximalaya.galaxy.business.service.enums.AgentContentType
import com.ximalaya.galaxy.business.service.support.RedisLockHelper
import com.ximalaya.galaxy.business.service.support.getAgentMessageCacheKey
import com.ximalaya.galaxy.business.service.vo.*
import com.ximalaya.galaxy.business.vo.ResultVo
import com.ximalaya.galaxy.business.worker.api.converter.CommonJobResponseConverter
import com.ximalaya.galaxy.business.worker.api.thrift.GalaxyBusinessWorkerJobService
import com.ximalaya.hot.track.service.vo.DifyRequestVo
import com.ximalaya.hot.track.service.vo.DifyResponseVo
import mu.KotlinLogging
import org.apache.commons.collections4.CollectionUtils
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter
import java.time.LocalDateTime
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit

/**
 *<AUTHOR>
 *@create 2025-05-20 10:54
 */
@BusinessComponent
class BizGalaxyBossImpl(
    private val sessionService: GalaxySessionService,
    private val phaseService: GalaxyPhaseService,
    private val blockService: GalaxyBlockService,
    private val workerJobIfaceService: GalaxyBusinessWorkerJobService.Iface,
    private val stringRedisTemplate: StringRedisTemplate,
    private val sseWriteBackSender: SseWriteBackSender,
) : BizGalaxyBoss {

    private var sseMap = ConcurrentHashMap<Pair<Long, Long>, SseEmitter>()

    @Transactional(rollbackFor = [Throwable::class])
    override fun createSessionAndFirstPhase(uid: Long, sessionName: String): Pair<Long, Long> {
        val sessionCode = createSession(uid, sessionName)
        val phaseCode = createPhase(uid, sessionCode, ALBUM_OUTLINE)
        return Pair(sessionCode, phaseCode)
    }

    override fun createSession(uid: Long, sessionName: String): Long {
        val now = LocalDateTime.now()

        val sessionEntity = GalaxySessionEntity().apply {
            this.uid = uid
            this.sessionName = sessionName
            this.sessionState = SessionState.INIT.code
            this.deletedAt = LogicDeleted.SAVE.getCode()
            this.createTime = now
            this.updateTime = now
        }

        GalaxyAsserts.assertTrue(sessionService.save(sessionEntity), ErrorCode.SESSION_ERROR, "创建会话失败")
        // sessionCode
        return sessionEntity.id!!
    }

    override fun createPhase(uid: Long, sessionCode: Long, phaseName: String): Long {
        val sessionEntity = sessionService.selectBySessionCode(sessionCode)
        GalaxyAsserts.assertNotNull(sessionEntity, ErrorCode.SESSION_ERROR, "会话不存在")
        GalaxyAsserts.assertEquals(sessionEntity!!.uid, uid, ErrorCode.SESSION_ERROR, "这里不是您的会话哦~")

        val now = LocalDateTime.now()

        val phaseEntity = GalaxyPhaseEntity().apply {
            this.sessionCode = sessionCode
            this.phaseName = phaseName
            this.phaseState = PhaseState.INIT.code
            this.deletedAt = LogicDeleted.SAVE.getCode()
            this.createTime = now
            this.updateTime = now
        }

        GalaxyAsserts.assertTrue(phaseService.save(phaseEntity), ErrorCode.PHASE_ERROR, "创建阶段失败")
        // phaseCode
        return phaseEntity.id!!
    }

    override fun updatePhaseState(sessionCode: Long, phaseCode: Long, phaseState: PhaseState): Boolean {
        return phaseService.ktUpdate()
            .eq(GalaxyPhaseEntity::sessionCode, sessionCode)
            .eq(GalaxyPhaseEntity::phaseCode, phaseCode)
            .set(GalaxyPhaseEntity::phaseState, phaseState.code)
            .set(GalaxyPhaseEntity::updateTime, LocalDateTime.now())
            .update()
    }

    override fun getSessionPhases(uid: Long, sessionCode: Long): SessionVo {
        val sessionEntity = sessionService.selectBySessionCode(sessionCode)
        GalaxyAsserts.assertNotNull(sessionEntity, ErrorCode.SESSION_ERROR, "会话不存在")
        GalaxyAsserts.assertEquals(sessionEntity!!.uid, uid, ErrorCode.SESSION_ERROR, "这里不是您的会话哦~")

        val phaseEntities = phaseService.selectBySessionCode(sessionCode)
        return SessionVo.of(sessionEntity, phaseEntities)
    }

    override fun connectSessionPhase(emitter: SseEmitter, uid: Long, sessionCode: Long, phaseCode: Long) {
        // 检查uid是否与sessionCode匹配
        val session = sessionService.selectBySessionCode(sessionCode)
        if (session == null) {
            emitter.send(ResultVo.failed<Unit>(ErrorCode.SESSION_ERROR, "会话不存在"))
            emitter.complete()
            return
        }

        if (session.uid != uid) {
            emitter.send(ResultVo.failed<Unit>(ErrorCode.SESSION_ERROR, "这里不是您的会话哦~"))
            emitter.complete()
            return
        }

        // 检查phase是否存在
        val phase = phaseService.selectBySessionCodeAndPhaseCode(sessionCode, phaseCode)
        if (phase == null) {
            emitter.send(ResultVo.failed<Unit>(ErrorCode.PHASE_ERROR, "阶段不存在"))
            emitter.complete()
            return
        }

        if (!connectStreamChannel(emitter, sessionCode, phaseCode)) {
            return
        }

        // 回放
        val messageKey = getAgentMessageCacheKey(sessionCode, phaseCode)
        val listSize = stringRedisTemplate.opsForList().size(messageKey) ?: 0

        if (listSize > 0) {
            // 获取Redis list中的所有消息并发送
            val messages = stringRedisTemplate.opsForList().range(messageKey, 0, listSize - 1)
            if (CollectionUtils.isNotEmpty(messages)) {
                emitPhaseMessageFromCache(emitter, messages!!)
            }
        }

        // 从db查 必然是已经停止了
        val blocks = blockService.ktQuery().eq(GalaxyBlockEntity::sessionCode, sessionCode)
            .eq(GalaxyBlockEntity::phaseCode, phaseCode)
            .eq(GalaxyBlockEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .list()

        if (CollectionUtils.isEmpty(blocks)) {
            // 没有找到数据块，抛出异常
            logger.error("未找到会话数据块, Session: {}, Phase: {}", sessionCode, phaseCode)

            val errorMessage = AgentContentGalaxyException().apply {
                this.sessionCode = sessionCode
                this.phaseCode = phaseCode
                this.errorCode = ErrorCode.BLOCK_ERROR
                this.errorMessage = "未找到会话数据块"
            }

            // 发送异常消息
            sseWriteBackSender.sendMessage(errorMessage)
            emitter.send(ResultVo.failed<Unit>(ErrorCode.BLOCK_ERROR, "未找到会话数据块"))
            emitter.complete()
            return
        }
        emitPhaseMessageFormDB(emitter, blocks)
    }

    private fun emitPhaseMessageFromCache(emitter: SseEmitter, messages: List<String>) {
        for (messageJson in messages) {
            val message = AgentContentProtocol.parseJson(messageJson)

            // 回放数据不需要内容检测，直接发送
            emitter.send(ResultVo.ok(message))

            // 如果是stop的
            if (message._type in setOf(AgentContentType.FINISH, AgentContentType.ERROR, AgentContentType.EXCEPTION)) {
                emitter.complete()
                return
            }
        }
    }

    private fun emitPhaseMessageFormDB(emitter: SseEmitter, blocks: List<GalaxyBlockEntity>) {
        for (block in blocks) {
            when (BlockType.parseCode(block.blockType)) {
                BlockType.USER -> {
                    emitter.send(
                        ResultVo.ok(
                            AgentContentBlockUser().apply {
                                this.blockCode = block.blockCode
                                this.content = block.content
                            }
                        ))
                }


                BlockType.TEXT -> {
                    emitter.send(ResultVo.ok(AgentContentBlockText().apply {
                        this.blockCode = block.blockCode
                    }))

                    // 回放数据不需要内容检测，直接发送
                    emitter.send(
                        ResultVo.ok(AgentContentText.parseJson(block.content!!))
                    )
                }

                BlockType.MCP_TOOL_CALL -> {
                    emitter.send(ResultVo.ok(AgentContentBlockToolCall().apply {
                        this.blockCode = block.blockCode
                    }))
                    emitter.send(
                        ResultVo.ok(AgentContentToolCall.parseJson(block.content!!))
                    )
                }

                BlockType.MCP_TOOL_RESULT -> {
                    emitter.send(ResultVo.ok(AgentContentBlockToolResult().apply {
                        this.blockCode = block.blockCode
                    }))
                    emitter.send(
                        ResultVo.ok(AgentContentToolResult.parseJson(block.content!!))
                    )
                }

                BlockType.DIFY_CALL -> {
                    emitter.send(ResultVo.ok(AgentContentBlockDifyCall().apply {
                        this.blockCode = block.blockCode
                        this.args = DifyRequestVo.parseJson(block.content!!)
                    }))
                }

                BlockType.DIFY_RESULT -> {
                    emitter.send(ResultVo.ok(AgentContentBlockDifyResult().apply {
                        this.blockCode = block.blockCode
                        this.result = DifyResponseVo.parseJson(block.content!!)
                    }))
                }

                else -> emitter.send(ResultVo.failed<Unit>(ErrorCode.SESSION_ERROR, "会话消息发生错误"))
            }

            emitter.complete()
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    override fun startSessionJob(emitter: SseEmitter, startVo: StartSessionJobVo) {
        if (!connectStreamChannel(emitter, startVo.sessionCode!!, startVo.phaseCode!!)) {
            return
        }

        // 发送worker
        val redisKey = RedisLockHelper.getPhaseOngoingLockKey(startVo.sessionCode!!, startVo.phaseCode!!)
        // 检查锁是否存在
        if (stringRedisTemplate.hasKey(redisKey)) {
            logger.warn("任务重复执行, Session: {}, Phase: {}", startVo.sessionCode, startVo.phaseCode)
            return
        }

        val thriftResponse = workerJobIfaceService.startSessionJob(startVo.toStartSessionJobRequest())

        val response = CommonJobResponseConverter.transform(thriftResponse)

        if (response.code != 200) {
            logger.error(
                "启动任务失败, Session: {}, Phase: {}, Code: {}, Message: {}",
                startVo.sessionCode,
                startVo.phaseCode,
                response.code,
                response.message
            )

            // 启动会话失败
            val message = AgentContentGalaxyException().apply {
                this.sessionCode = startVo.sessionCode
                this.phaseCode = startVo.phaseCode
                this.errorCode = ErrorCode.SESSION_ERROR
                this.errorMessage = response.message ?: "启动会话失败"
            }
            updatePhaseState(startVo.sessionCode!!, startVo.phaseCode!!, PhaseState.FAILED)
            blockService.createBlock(
                startVo.sessionCode!!,
                startVo.phaseCode!!,
                BlockType.EXCEPTION,
                message.toJson()
            )

            val result = ResultVo.failed<Unit>(message.errorCode!!, message.errorMessage)
            emitter.send(result)
            emitter.complete()
            return
        }
    }

    private fun connectStreamChannel(emitter: SseEmitter, sessionCode: Long, phaseCode: Long): Boolean {
        val connectRedisKey = RedisLockHelper.getPhaseConnectLockKey(sessionCode, phaseCode)
        emitter.onCompletion {
            sseMap.remove(Pair(sessionCode, phaseCode))
            stringRedisTemplate.delete(connectRedisKey)
        }

        // 设置有效期为 20 分钟
        val isLockAcquired = stringRedisTemplate.opsForValue()
            .setIfAbsent(connectRedisKey, phaseCode.toString(), 20, TimeUnit.MINUTES)
        if (isLockAcquired != true) {
            logger.warn("不允许同时打开多个窗口, Session: {}, Phase: {}", sessionCode, phaseCode)

            emitter.send(ResultVo.failed<Unit>(ErrorCode.SESSION_ERROR, "不允许同时打开多个窗口"))
            emitter.complete()
            return false
        }


        sseMap.computeIfAbsent(Pair(sessionCode, phaseCode)) {
            emitter
        }

        return true
    }

    override fun sendSseWriteBackMessage(agentMessage: AgentContentProtocol) {
        sseMap[agentMessage.toPair()]?.let { emitter ->
            emitter.send(ResultVo.ok(agentMessage))

            // 如果是stop的
            if (agentMessage._type in setOf(
                    AgentContentType.FINISH,
                    AgentContentType.ERROR,
                    AgentContentType.EXCEPTION
                )
            ) {
                emitter.complete()
            }
        }
    }

    override fun updateBlock(sessionCode: Long, phaseCode: Long, blockCode: Long, content: String): Boolean {
        return blockService.updateBlock(sessionCode, phaseCode, blockCode, content)
    }

    companion object {

        private val logger = KotlinLogging.logger { }

    }

}