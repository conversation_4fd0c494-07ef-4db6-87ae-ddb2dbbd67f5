package com.ximalaya.galaxy.business.business.boss.vo

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.annotation.JSONField
import com.ximalaya.galaxy.business.worker.api.thrift.entity.StartToolJobRequest
import org.apache.commons.collections4.CollectionUtils

/**
 *<AUTHOR>
 *@create 2025-05-28 17:26
 */
data class StartToolJobVo(

  var sessionCode: Long,

  var phaseCode: Long,

  var toolId: Long,

  var inputs: Map<String, String?>?,

  var needRemoveBlockCodes: List<Long>?,
) {

  @JSONField(serialize = false, deserialize = false)
  fun toStartToolJobRequest(): StartToolJobRequest {
    return StartToolJobRequest.Builder()
      .setSessionCode(this.sessionCode)
      .setPhaseCode(this.phaseCode)
      .setToolId(this.toolId)
      .setArgs(JSON.toJSONString(this.inputs))
      .setNeedRemoveBlockCodes(if (CollectionUtils.isEmpty(this.needRemoveBlockCodes)) emptyList() else needRemoveBlockCodes)
      .build()
  }

}