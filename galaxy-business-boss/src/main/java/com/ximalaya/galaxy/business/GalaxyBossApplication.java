package com.ximalaya.galaxy.business;

import com.ximalaya.eros.cache.config.ErosCacheJedisAutoConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @create 2023-02-15 16:02
 */
@SpringBootApplication(exclude = {ErosCacheJedisAutoConfig.class})
@EnableScheduling
public class GalaxyBossApplication extends SpringBootServletInitializer {

    public static void main(String[] args) {
        SpringApplication.run(GalaxyBossApplication.class);
    }

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        return builder.sources(GalaxyBossApplication.class);
    }

}
