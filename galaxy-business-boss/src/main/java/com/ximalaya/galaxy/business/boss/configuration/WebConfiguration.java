package com.ximalaya.galaxy.business.boss.configuration;

import com.ximalaya.galaxy.business.boss.interceptor.DevAuthInterceptor;
import com.ximalaya.galaxy.business.boss.interceptor.HttpRequestLogFilter;
import com.ximalaya.galaxy.business.boss.interceptor.InvitationAuthInterceptor;
import java.util.Optional;
import javax.servlet.MultipartConfigElement;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.data.convert.Jsr310Converters.StringToLocalDateConverter;
import org.springframework.data.convert.Jsr310Converters.StringToLocalDateTimeConverter;
import org.springframework.format.FormatterRegistry;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @create 2023-08-04 01:37
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class WebConfiguration implements WebMvcConfigurer {

  private final InvitationAuthInterceptor invitationAuthInterceptor;

  @Autowired(required = false)
  private DevAuthInterceptor devAuthInterceptor;


  /**
   * 处理开发测试环境跨域请求, 方便联调
   */
  @Bean
  @Profile({"dev"})
  public FilterRegistrationBean<CorsFilter> corsFilter() {
    UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
    CorsConfiguration config = new CorsConfiguration();
    config.setAllowCredentials(true);
    config.addAllowedOriginPattern(CorsConfiguration.ALL);
    config.addAllowedHeader(CorsConfiguration.ALL);
    config.addAllowedMethod(CorsConfiguration.ALL);
    // CORS 配置对所有接口都有效
    source.registerCorsConfiguration("/**", config);
    FilterRegistrationBean<CorsFilter> registrationBean = new FilterRegistrationBean<>();
    CorsFilter corsFilter = new CorsFilter(source);
    registrationBean.setFilter(corsFilter);
    registrationBean.setOrder(0);
    return registrationBean;
  }

  @Bean
  public MultipartConfigElement multipartConfig() {
    int maxSize = 100 * 1024 * 1024;
    return new MultipartConfigElement("/tmp", maxSize, maxSize, maxSize);
  }

  @Override
  public void addInterceptors(@NotNull InterceptorRegistry registry) {
    // 开发环境
    Optional.ofNullable(devAuthInterceptor)
        .ifPresent(interceptor -> {
          registry.addInterceptor(interceptor)
              .addPathPatterns("/**")
              .excludePathPatterns("/swagger*/**", "/webjars*/**", "/error*/**", "/v2/api*/**",
                  "/test/**", "/**/*.js", "/**/*.css", "/**/*.png ", "/**/*.jpg", "/**/*.jpeg ");
        });

    registry.addInterceptor(invitationAuthInterceptor)
        .order(Integer.MAX_VALUE)
        .addPathPatterns("/**")
        .excludePathPatterns("/swagger*/**", "/webjars*/**", "/error*/**", "/v2/api*/**",
            "/test/**", "/**/*.js", "/**/*.css", "/**/*.png ", "/**/*.jpg", "/**/*.jpeg ");
  }

  @Bean
  @Profile({"dev", "test"})
  public FilterRegistrationBean<HttpRequestLogFilter> httpRequestLogFilter() {
    FilterRegistrationBean<HttpRequestLogFilter> registrationBean = new FilterRegistrationBean<>();
    registrationBean.setFilter(new HttpRequestLogFilter());
    registrationBean.addUrlPatterns("/*");
    return registrationBean;
  }

  @Override
  public void addResourceHandlers(ResourceHandlerRegistry registry) {
    registry.addResourceHandler("swagger-ui.html")
        .addResourceLocations("classpath:/META-INF/resources/");
    registry.addResourceHandler("/webjars/**")
        .addResourceLocations("classpath:/META-INF/resources/webjars/");
  }

  @Override
  public void addFormatters(FormatterRegistry registry) {
    registry.addConverter(StringToLocalDateTimeConverter.INSTANCE);
    registry.addConverter(StringToLocalDateConverter.INSTANCE);
  }

}
