package com.ximalaya.galaxy.business.boss.configuration;

import com.alibaba.fastjson.JSON;
import com.ximalaya.galaxy.business.common.enums.ErrorCode;
import com.ximalaya.galaxy.business.common.exception.GalaxyException;
import com.ximalaya.galaxy.business.vo.ResultVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2022-11-25 10:15
 */
@Slf4j
@ControllerAdvice
public class AppExceptionHandler {

    @ResponseBody
    @ExceptionHandler(Exception.class)
    public ResultVo<Void> handleException(Exception ex, HttpServletRequest request) {
        log.error("未知异常：{}, {}", ex.getMessage(), requestInfo(request), ex);
        return ResultVo.failed(ErrorCode.UNKNOWN_ERROR.getCode(), ex.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(GalaxyException.class)
    public ResultVo<Void> handleGalaxyBusinessException(GalaxyException ex,
                                                        HttpServletRequest request) {
        log.error("业务异常：{}, {}", ex.getMessage(), requestInfo(request));
        return ResultVo.failed(ex);
    }

    @ResponseBody
    @ExceptionHandler(NullPointerException.class)
    public ResultVo<Void> handleNullPointException(NullPointerException ex,
                                                   HttpServletRequest request) {
        log.error("空指针：{}, {}", ex.getMessage(), requestInfo(request));
        return ResultVo.failed(ErrorCode.UNKNOWN_ERROR.getCode(), ex.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(IllegalArgumentException.class)
    public ResultVo<Void> handleIllegalArgumentException(IllegalArgumentException ex,
                                                         HttpServletRequest request) {
        log.error("非法参数：{}, {}", ex.getMessage(), requestInfo(request), ex);
        return ResultVo.failed(ErrorCode.PARAMS_ERROR.getCode(), ex.getMessage());
    }

    @ResponseBody
    @ExceptionHandler(IllegalStateException.class)
    public ResultVo<Void> handleIllegalStateException(IllegalStateException ex,
                                                      HttpServletRequest request) {
        log.error("非法状态：{}, {}", ex.getMessage(), requestInfo(request), ex);
        return ResultVo.failed(ErrorCode.ASSERT_ERROR.getCode(), ex.getMessage());
    }

    private String requestInfo(HttpServletRequest request) {
        String requestUri = request.getRequestURI();
        Map<String, String[]> parameterMap = request.getParameterMap();
        return String.format("Request Url: %s, Params is : %s", requestUri,
                JSON.toJSONString(parameterMap));
    }

}
