package com.ximalaya.galaxy.business.boss.controller

import com.ximalaya.common.auth.common.subject.UserContext
import com.ximalaya.galaxy.business.business.BizAuth
import com.ximalaya.galaxy.business.business.boss.BossFootballConfigBean
import com.ximalaya.galaxy.business.vo.ResultVo
import com.ximalaya.galaxy.business.vo.UserVo
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.web.bind.annotation.*

/**
 *<AUTHOR>
 *@create 2025-01-15 11:11
 */
@Api(tags = ["权限API"])
@RestController
@RequestMapping("/api/auth")
class AuthController(
  private val bizAuth: BizAuth,
  private val footballConfigBean: BossFootballConfigBean,
) {

  @ApiOperation(value = "当前用户")
  @GetMapping("/user")
  fun getCurrentUser(): ResultVo<UserVo> {
    val user = UserContext.getUid()?.let {
      bizAuth.getUser(it)
    }

    return user?.let {
      ResultVo.ok(it)
    } ?: ResultVo.failed("获取不到当前用户")
  }

  @ApiOperation(value = "当前用户UID")
  @GetMapping("/uid")
  fun getUid(): ResultVo<Long> {
    val uid = UserContext.getUid()

    return uid?.let {
      ResultVo.ok(it)
    } ?: ResultVo.failed("获取不到当前用户UID")
  }

  @ApiOperation(value = "验证邀请码")
  @PostMapping("/i-code/{invitationCode}")
  fun useInvitationCode(@PathVariable invitationCode: String): ResultVo<Boolean> {
    val uid = UserContext.getUid()
    val result = bizAuth.useInvitationCode(invitationCode, uid)
    return ResultVo.ok(result)
  }

  @ApiOperation(value = "是否内部用户")
  @GetMapping("/inner")
  fun isInnerUser(): ResultVo<Boolean> {
    val uid = UserContext.getUid() ?: return ResultVo.failed("获取不到当前用户UID")

    return ResultVo.ok(footballConfigBean.isInnerUser(uid))
  }

}