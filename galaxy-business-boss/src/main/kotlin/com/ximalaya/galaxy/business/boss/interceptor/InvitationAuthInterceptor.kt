package com.ximalaya.galaxy.business.boss.interceptor

import com.ximalaya.common.auth.common.subject.UserContext
import com.ximalaya.galaxy.business.common.support.BooleanSupport
import com.ximalaya.galaxy.business.repo.service.InvitationCodeService
import com.ximalaya.galaxy.business.service.support.getUserInvitationCacheKey
import mu.KotlinLogging
import org.redisson.api.RedissonClient
import org.redisson.client.codec.IntegerCodec
import org.springframework.stereotype.Component
import org.springframework.web.servlet.HandlerInterceptor
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

/**
 *<AUTHOR>
 *@create 2025-05-15 16:04
 */
@Component
class InvitationAuthInterceptor(
  private val redissonClient: RedissonClient,
  private val invitationCodeService: InvitationCodeService
) : HandlerInterceptor {

  override fun preHandle(request: HttpServletRequest, response: HttpServletResponse, handler: Any): Boolean {
    // 检查是否已经被邀请了
    val uid = UserContext.getUid()

    val invitationBucket = redissonClient.getBucket<Int>(getUserInvitationCacheKey(uid), IntegerCodec.INSTANCE)
    val invitation = invitationBucket.get()

    // redis不存在
    if (!invitationBucket.isExists || invitation == null) {
      val entity = invitationCodeService.selectByUid(uid)

      // 被邀请了
      if (entity != null) {
        // 设置redis
        invitationBucket.set(BooleanSupport.decodeInt(true))
        return true
      }

      // 没被邀请
      invitationBucket.set(BooleanSupport.decodeInt(false))
      logger.error("uid: {} 未被邀请", uid)

      response.status = 401
      return false
    }

    return if (BooleanSupport.encodeInt(invitation)) {
      // 被邀请了
      true
    } else {

      // 没被邀请
      logger.error("uid: {} 未被邀请", uid)

      response.status = 401
      false
    }
  }

  companion object {

    val logger = KotlinLogging.logger { }

  }

}