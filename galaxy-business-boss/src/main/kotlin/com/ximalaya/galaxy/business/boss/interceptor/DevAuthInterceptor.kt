package com.ximalaya.galaxy.business.boss.interceptor

import com.ximalaya.common.auth.common.subject.SsoSession
import com.ximalaya.common.auth.common.subject.UserContext
import com.ximalaya.common.auth.common.subject.UserType
import com.ximalaya.galaxy.business.service.support.EnvSupport
import mu.KotlinLogging
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Component
import org.springframework.web.servlet.HandlerInterceptor
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse

/**
 *<AUTHOR>
 *@create 2025-05-08 17:18
 */
@Profile("dev")
@Component
class DevAuthInterceptor : HandlerInterceptor {

    override fun preHandle(
        request: HttpServletRequest,
        response: HttpServletResponse,
        handler: Any
    ): Boolean {

        if (EnvSupport.isDev()) {
            val ssoSession = SsoSession(UserType.USER, DEV_UID, "")
            UserContext.hold(ssoSession)
            return true
        }

        return true
    }

    override fun afterCompletion(
        request: HttpServletRequest,
        response: HttpServletResponse,
        handler: Any,
        ex: java.lang.Exception?
    ) {
        if (EnvSupport.isDev()) {
            UserContext.discard()
        }
    }

    companion object {

        private val logger = KotlinLogging.logger { }

        private const val DEV_UID = 1285236L

    }

}