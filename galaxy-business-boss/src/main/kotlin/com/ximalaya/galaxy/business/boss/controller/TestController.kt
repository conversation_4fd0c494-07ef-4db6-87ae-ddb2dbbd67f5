package com.ximalaya.galaxy.business.boss.controller

import com.ximalaya.common.auth.common.subject.UserContext
import com.ximalaya.galaxy.business.business.BizAuth
import com.ximalaya.galaxy.business.business.boss.BossFootballConfigBean
import com.ximalaya.galaxy.business.common.HELLO_WORLD
import com.ximalaya.galaxy.business.common.TEST_LOCK_KEY
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.exception.GalaxyException
import com.ximalaya.galaxy.business.repo.entity.GalaxyBlockEntity
import com.ximalaya.galaxy.business.repo.entity.GalaxyPhaseEntity
import com.ximalaya.galaxy.business.repo.entity.GalaxySessionEntity
import com.ximalaya.galaxy.business.repo.enums.AuditState
import com.ximalaya.galaxy.business.repo.enums.BlockType
import com.ximalaya.galaxy.business.repo.enums.LogicDeleted
import com.ximalaya.galaxy.business.repo.enums.SessionState
import com.ximalaya.galaxy.business.repo.service.GalaxyBlockService
import com.ximalaya.galaxy.business.repo.service.GalaxyPhaseService
import com.ximalaya.galaxy.business.repo.service.GalaxySessionService
import com.ximalaya.galaxy.business.vo.ResultVo
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import io.swagger.annotations.ApiParam
import mu.KotlinLogging
import org.apache.commons.lang3.StringUtils
import org.redisson.api.RLock
import org.redisson.api.RedissonClient
import org.springframework.context.annotation.Profile
import org.springframework.web.bind.annotation.*
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.concurrent.TimeUnit

/**
 *<AUTHOR> Shengzhe
 *@create 2022-11-26 22:22
 */
@Api(tags = ["测试API"])
@Profile("dev", "test")
@RestController
@RequestMapping("/test")
class TestController(
  private val footballConfigBean: BossFootballConfigBean,
  private val redissonClient: RedissonClient,
  private val bizAuth: BizAuth,

  private val galaxySessionService: GalaxySessionService,
  private val galaxyPhaseService: GalaxyPhaseService,
  private val galaxyBlockService: GalaxyBlockService,
) {

  @ApiOperation("hello")
  @GetMapping("/echo")
  fun echo(
    @ApiParam("hello swagger") @RequestParam(
      value = "content",
      required = false
    ) content: String?
  ): ResultVo<String> {
    val result = when {
      StringUtils.isNotBlank(content) -> content
      else -> HELLO_WORLD
    }
    return ResultVo.ok(result)
  }

  @ApiOperation("hello-football")
  @GetMapping("/football")
  fun football(): ResultVo<String> = ResultVo.ok(footballConfigBean.helloFootball)

  @ApiOperation("is-admin")
  @GetMapping("/is-admin")
  fun isAdmin(): ResultVo<Boolean> {
    val uid = UserContext.getUid()

    return ResultVo.ok(footballConfigBean.isAdmin(uid))
  }

  @ApiOperation("redisson")
  @GetMapping("/redisson")
  fun redisson(): ResultVo<String> {
    val rLock: RLock = redissonClient.getLock(TEST_LOCK_KEY)
    try {
      val result = when (rLock.tryLock(0, 1, TimeUnit.SECONDS)) {
        true -> "tryLock success"
        false -> "tryLock failed"
      }

      return ResultVo.ok(result)

    } catch (_: InterruptedException) {
      Thread.currentThread().interrupt()
      throw GalaxyException(ErrorCode.THREAD_INTERRUPTED)
    } finally {
      if (rLock.isLocked && rLock.isHeldByCurrentThread) {
        rLock.unlock()
      }
    }
  }

  @ApiOperation("i-code")
  @GetMapping("/i-code")
  fun generateInvitationCode(): ResultVo<Boolean> {
    val result = bizAuth.generateInvitationCode(100)
    return ResultVo.ok(result)
  }

  @ApiOperation("save-session")
  @PostMapping("/session/{uid}/{year}/{month}/{day}")
  fun saveSession(@PathVariable uid: Long, @PathVariable year: Int, @PathVariable month: Int, @PathVariable day: Int): ResultVo<GalaxySessionEntity> {
    val date = LocalDate.of(year, month, day)
    val time = LocalTime.now()

    val entity = GalaxySessionEntity().apply {
      this.uid = uid
      this.sessionName = "test"
      this.sessionState = SessionState.RUNNING.code
      this.deletedAt = LogicDeleted.SAVE.getCode()
      this.createTime = date.atTime(time)
      this.updateTime = date.atTime(time)
    }

    galaxySessionService.save(entity)

    val result = galaxySessionService.ktQuery()
      .eq(GalaxySessionEntity::sessionCode, entity.id)
      .one()

    logger.info("Save GalaxySessionEntity -> {}", result)

    return ResultVo.ok(result)
  }

  @ApiOperation("save-phase")
  @PostMapping("/phase/{sessionCode}")
  fun savePhase(@PathVariable sessionCode: Long): ResultVo<GalaxyPhaseEntity> {
    val now = LocalDateTime.now()

    val entity = GalaxyPhaseEntity().apply {
      this.sessionCode = sessionCode
      this.phaseName = "test"
      this.deletedAt = LogicDeleted.SAVE.getCode()
      this.createTime = now
      this.updateTime = now
    }

    galaxyPhaseService.save(entity)

    val result = galaxyPhaseService.ktQuery()
      .eq(GalaxyPhaseEntity::phaseCode, entity.id)
      .one()

    logger.info("Save GalaxyPhaseEntity -> {}", result)

    return ResultVo.ok(result)
  }

  @ApiOperation("save-block")
  @PostMapping("/block/{sessionCode}/{phaseCode}")
  fun saveBlock(@PathVariable sessionCode: Long, @PathVariable phaseCode: Long): ResultVo<GalaxyBlockEntity> {
    val now = LocalDateTime.now()

    val entity = GalaxyBlockEntity().apply {
      this.sessionCode = sessionCode
      this.phaseCode = phaseCode
      this.blockType = BlockType.USER.code
      this.content = "test"
      this.auditState = AuditState.SUCCESS.code
      this.deletedAt = LogicDeleted.SAVE.getCode()
      this.createTime = now
      this.updateTime = now
    }

    galaxyBlockService.save(entity)

    val result = galaxyBlockService.ktQuery()
      .eq(GalaxyBlockEntity::blockCode, entity.id)
      .one()

    logger.info("Save GalaxyPhaseEntity -> {}", result)

    return ResultVo.ok(result)
  }

  companion object {

    private val logger = KotlinLogging.logger { }

  }


}