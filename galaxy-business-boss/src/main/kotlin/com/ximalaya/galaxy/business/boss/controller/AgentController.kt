package com.ximalaya.galaxy.business.boss.controller

import com.ximalaya.common.auth.common.subject.UserContext
import com.ximalaya.galaxy.business.boss.support.buildFailedResult
import com.ximalaya.galaxy.business.business.boss.BizGalaxyBoss
import com.ximalaya.galaxy.business.business.boss.vo.CreatePhaseVo
import com.ximalaya.galaxy.business.business.boss.vo.CreateSessionVo
import com.ximalaya.galaxy.business.business.boss.vo.SessionVo
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.vo.ResultVo
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.validation.BindingResult
import org.springframework.web.bind.annotation.*

/**
 *<AUTHOR>
 *@create 2025-05-25 23:21
 */
@Api(tags = ["智能体API"])
@RestController
@RequestMapping("/api/agent")
class AgentController(
    private val bizGalaxyBoss: BizGalaxyBoss,
) {

    @ApiOperation(value = "创建会话和第一个阶段")
    @PostMapping("/session-and-1st-phase")
    fun createSessionAndFirstPhase(
        @RequestBody vo: CreateSessionVo,
        bindingResult: BindingResult
    ): ResultVo<Pair<Long, Long>> {
        val uid = UserContext.getUid() ?: return ResultVo.failed(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID")

        return when (bindingResult.hasErrors()) {
            true -> bindingResult.buildFailedResult()
            false -> {
                ResultVo.ok(bizGalaxyBoss.createSessionAndFirstPhase(uid, vo.sessionName!!))
            }
        }
    }

    @ApiOperation(value = "创建会话")
    @PostMapping("/session")
    fun createSession(@RequestBody vo: CreateSessionVo, bindingResult: BindingResult): ResultVo<Long> {
        val uid = UserContext.getUid() ?: return ResultVo.failed(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID")

        return when (bindingResult.hasErrors()) {
            true -> bindingResult.buildFailedResult()
            false -> {
                ResultVo.ok(bizGalaxyBoss.createSession(uid, vo.sessionName!!))
            }
        }
    }

    @ApiOperation(value = "创建阶段")
    @PostMapping("/session/{sessionCode}/phase")
    fun createPhase(
        @PathVariable sessionCode: Long,
        @RequestBody vo: CreatePhaseVo,
        bindingResult: BindingResult
    ): ResultVo<Long> {
        val uid = UserContext.getUid() ?: return ResultVo.failed(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID")

        return when (bindingResult.hasErrors()) {
            true -> bindingResult.buildFailedResult()
            false -> {
                ResultVo.ok(bizGalaxyBoss.createPhase(uid, sessionCode, vo.phaseName!!))
            }
        }
    }

    @ApiOperation(value = "获取会话信息")
    @GetMapping("/session/{sessionCode}")
    fun getSessionPhases(@PathVariable sessionCode: Long): ResultVo<SessionVo> {
        val uid = UserContext.getUid() ?: return ResultVo.failed(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID")

        return ResultVo.ok(bizGalaxyBoss.getSessionPhases(uid, sessionCode))
    }

    @ApiOperation(value = "更新block内容")
    @PostMapping("/session/{sessionCode}/phase/{phaseCode}/block/{blockCode}/content")
    fun updateBlockContent(
        @PathVariable sessionCode: Long, @PathVariable phaseCode: Long, @PathVariable blockCode: Long,
        @RequestBody content: String
    ): ResultVo<Boolean> {
        return ResultVo.ok(bizGalaxyBoss.updateBlock(sessionCode, phaseCode, blockCode, content))
    }

}