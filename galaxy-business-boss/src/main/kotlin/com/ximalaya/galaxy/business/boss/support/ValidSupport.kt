package com.ximalaya.galaxy.business.boss.support

import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.vo.ResultVo
import org.springframework.validation.BindingResult

/**
 *<AUTHOR>
 *@create 2023-08-11 18:09
 */
fun <T> BindingResult.buildFailedResult(errorCode: ErrorCode? = null): ResultVo<T> {
  val message = when (val fieldError = this.fieldError) {
    null -> "很抱歉，您填写的信息有错，请检查~"
    else -> fieldError.defaultMessage
  }

  return ResultVo.failed(errorCode ?: ErrorCode.PARAMS_ERROR, message)
}