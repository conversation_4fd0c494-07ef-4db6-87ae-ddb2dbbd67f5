package com.ximalaya.galaxy.business.boss.controller

import com.ximalaya.common.auth.common.subject.UserContext
import com.ximalaya.galaxy.business.boss.support.buildFailedResult
import com.ximalaya.galaxy.business.business.BizAgent
import com.ximalaya.galaxy.business.business.boss.BizGalaxyBoss
import com.ximalaya.galaxy.business.business.boss.vo.SessionPromptVo
import com.ximalaya.galaxy.business.business.boss.vo.StartSessionJobVo
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.repo.service.GalaxyPromptService
import com.ximalaya.galaxy.business.vo.ResultVo
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.apache.commons.lang3.StringUtils
import org.springframework.validation.BindingResult
import org.springframework.web.bind.annotation.*
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter

/**
 *<AUTHOR>
 *@create 2025-05-20 17:03
 */
@Api(tags = ["SSE API"])
@RestController
@RequestMapping("/api/stream")
class StreamController(
  private val bizAgent: BizAgent,
  private val bizGalaxyBoss: BizGalaxyBoss,
  private val promptService: GalaxyPromptService,
) {

  @ApiOperation("hello")
  @PostMapping("/hello")
  fun hello(): SseEmitter {
    val emitter = SseEmitter(600_000)
    bizAgent.hello(emitter)
    return emitter
  }

  @ApiOperation("连接 会话-阶段 用来获取最新状态")
  @PostMapping("/connect/session/{sessionCode}/phase/{phaseCode}")
  fun connectSessionPhase(@PathVariable sessionCode: Long, @PathVariable phaseCode: Long): SseEmitter {
    val emitter = SseEmitter(600_000)

    val uid = UserContext.getUid()
    if (uid == null) {
      emitter.send(ResultVo.failed<Unit>(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID"))

      emitter.complete()
      return emitter
    }

    bizGalaxyBoss.connectSessionPhase(emitter, uid, sessionCode, phaseCode)
    return emitter
  }

  @ApiOperation("开启 会话-阶段 任务")
  @PostMapping("/job/session/{sessionCode}/phase/{phaseCode}")
  fun startSessionJob(@PathVariable sessionCode: Long, @PathVariable phaseCode: Long, @RequestBody promptVo: SessionPromptVo, bindingResult: BindingResult): SseEmitter {
    val uid = UserContext.getUid()

    val emitter = SseEmitter(600_000)

    if (uid == null) {
      emitter.send(ResultVo.failed<Unit>(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID"))

      emitter.complete()
      return emitter
    }

    if (bindingResult.hasErrors()) {
      emitter.send(bindingResult.buildFailedResult<Unit>())

      emitter.complete()
      return emitter
    }

    val systemPrompt = promptService.selectByName(promptVo.systemPromptName!!)
    if (systemPrompt == null || StringUtils.isBlank(systemPrompt.content)) {
      emitter.send(ResultVo.failed<Unit>(ErrorCode.CONTENT_NOT_FOUND, "系统提示词不存在"))

      emitter.complete()
      return emitter
    }

    bizGalaxyBoss.startSessionJob(emitter, StartSessionJobVo(sessionCode, phaseCode, systemPrompt.content, promptVo.prompt!!))
    return emitter
  }

}