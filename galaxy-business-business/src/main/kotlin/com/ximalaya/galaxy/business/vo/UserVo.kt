package com.ximalaya.galaxy.business.vo

import com.ximalaya.galaxy.business.common.support.OptionalString
import com.ximalaya.galaxy.business.service.support.EnvSupport
import com.ximalaya.service.profile.model.BasicUserInfo

/**
 *<AUTHOR>
 *@create 2025-05-08 16:26
 */
class UserVo {

    var uid: Long? = null

    var nickname: String? = null

    var logoPic: String? = null

    companion object {

        const val TEST_PREFIX = "http://imagev2.test.ximalaya.com/"
        const val PROD_PREFIX = "https://imagev2.ximalaya.com/"

        fun of(vo: BasicUserInfo): UserVo {
            return UserVo().apply {
                this.uid = vo.uid
                this.nickname = vo.nickname
                OptionalString.ofNullable(vo.logoPic)
                    .ifPresent {
                        this.logoPic = if (EnvSupport.online()) {
                            PROD_PREFIX + it
                        } else {
                            TEST_PREFIX + it
                        }
                    }

            }
        }

    }

}