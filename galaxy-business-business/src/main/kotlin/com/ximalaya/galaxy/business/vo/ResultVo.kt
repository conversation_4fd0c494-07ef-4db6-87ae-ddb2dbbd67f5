package com.ximalaya.galaxy.business.vo

import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.exception.GalaxyException


/**
 *<AUTHOR>
 *@create 2022-11-25 10:17
 */
data class ResultVo<out T>(
  val code: Int,
  val message: String?,
  val data: T?
) {

  companion object {
    private const val SUCCESS = 200
    private const val COMMON_FAILED = 5000
    private const val SUCCESS_MESSAGE = "成功"

    @JvmStatic
    fun <T> ok(): ResultVo<T> {
      return ResultVo(SUCCESS, SUCCESS_MESSAGE, null)
    }

    @JvmStatic
    fun <T> ok(data: T?): ResultVo<T> {
      return ResultVo(SUCCESS, SUCCESS_MESSAGE, data)
    }

    @JvmStatic
    fun <T> failed(message: String?): ResultVo<T> {
      return ResultVo(COMMON_FAILED, message, null)
    }

    @JvmStatic
    fun <T> failed(errorCode: ErrorCode): ResultVo<T> {
      return ResultVo(errorCode.code, errorCode.message, null)
    }

    @JvmStatic
    fun <T> failed(errorCode: ErrorCode, message: String? = null): ResultVo<T> {
      return ResultVo(errorCode.code, message ?: errorCode.message, null)
    }

    @JvmStatic
    fun <T> failed(code: Int, message: String): ResultVo<T> {
      return ResultVo(code, message, null)
    }

    @JvmStatic
    fun <T> failed(ex: GalaxyException): ResultVo<T> {
      val errorCode = ex.errorCode ?: ErrorCode.UNKNOWN_ERROR
      val message = ex.message ?: errorCode.message
      return failed(errorCode.code, message)
    }

  }

}