package com.ximalaya.galaxy.business.business.impl

import com.ximalaya.galaxy.business.business.BizAgent
import com.ximalaya.galaxy.business.business.BusinessComponent
import com.ximalaya.galaxy.business.service.AgentService
import com.ximalaya.galaxy.business.service.enums.AgentMessageRole
import com.ximalaya.galaxy.business.service.vo.AgentChatRequestVo
import com.ximalaya.galaxy.business.service.vo.AgentContentText
import com.ximalaya.galaxy.business.service.vo.AgentMessageVo
import com.ximalaya.galaxy.business.support.AgentStreamingResultPrinter
import okhttp3.Callback
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter

/**
 *<AUTHOR>
 *@create 2025-05-21 16:46
 */
@BusinessComponent
class BizAgentImpl(
  private val agentService: AgentService
) : BizAgent {

  override fun hello(emitter: SseEmitter) {
    val vo = AgentChatRequestVo().apply {
      this.system = "You are a helpful assistant."
      this.messages = mutableListOf(
        AgentMessageVo(AgentMessageRole.USER).apply {
          this.content = mutableListOf(
            AgentContentText().apply {
              this.text = "找一篇个人成长的文章"
            }
          )
        }
      )
    }
    agentService.chat(vo, AgentStreamingResultPrinter(emitter))
  }

  override fun chat(vo: AgentChatRequestVo, callback: Callback) {
    agentService.chat(vo, callback)
  }

}