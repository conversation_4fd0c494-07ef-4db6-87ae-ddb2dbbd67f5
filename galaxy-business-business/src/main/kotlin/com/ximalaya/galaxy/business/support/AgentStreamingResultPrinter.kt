package com.ximalaya.galaxy.business.support

import com.alibaba.fastjson.JSON
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.exception.GalaxyException
import com.ximalaya.galaxy.business.service.enums.AgentContentType
import com.ximalaya.galaxy.business.service.vo.AgentContentError
import com.ximalaya.galaxy.business.service.vo.AgentContentProtocol
import com.ximalaya.galaxy.business.vo.ResultVo
import mu.KotlinLogging
import okhttp3.Call
import okhttp3.Callback
import okhttp3.Response
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter
import java.io.IOException

/**
 *<AUTHOR>
 *@create 2025-05-21 16:47
 */
class AgentStreamingResultPrinter(
  var emitter: SseEmitter? = null,
) : Callback {


  override fun onFailure(call: Call, e: IOException) {
    logger.error("data: {}\n\n", JSON.toJSONString(ResultVo.failed<Unit>(ErrorCode.INNER_ERROR, e.message)))
  }

  override fun onResponse(call: Call, response: Response) {

    response.body()?.source()?.let { source ->
      if (!response.isSuccessful) {
        throw IOException("Unexpected code $response")
      }

      while (!source.exhausted()) {

        val line = source.readUtf8LineStrict()

        if (line.startsWith("data: ")) { // 处理SSE格式数据
          val data = line.substring(6).trim()

          val contentProtocol = AgentContentProtocol.parseJson(data)


          if (contentProtocol._type == AgentContentType.FINISH) {
            val result = JSON.toJSONString(ResultVo.ok(contentProtocol))
            logger.info("data: {}\n\n", result)
            emitter?.send(result)
            emitter?.complete()
            return
          } else if (contentProtocol._type == AgentContentType.ERROR) {
            val error = contentProtocol as AgentContentError
            val result = JSON.toJSONString(ResultVo.failed<Unit>(GalaxyException(ErrorCode.CALL_AGENT_ERROR, JSON.toJSONString(error.error))))
            logger.info("data: {}\n\n", result)
            emitter?.send(result)
            emitter?.complete()
            return
          }

          val result = JSON.toJSONString(ResultVo.ok(contentProtocol))
          logger.info("data: {}\n\n", result)
          emitter?.send(result)

        }
      }
    }

  }

  companion object {
    private val logger = KotlinLogging.logger { }
  }


}