package com.ximalaya.galaxy.business.support

/**
 *<AUTHOR>
 *@create 2023-08-29 15:20
 */
object InvitationCodeGenerator {

  private val CHARS = ('A'..'Z') + ('0'..'9')

  fun generate(count: Int, existCodes: MutableSet<String>): List<String> {
    var num = 0
    val result = mutableListOf<String>()
    while (num < count) {
      val newCode = generateCode()
      if (existCodes.contains(newCode)) {
        continue
      }
      existCodes.add(newCode)
      result.add(newCode)
      num++
    }
    return result
  }

  private fun generateCode(): String {
    return (1..6)
      .map { CHARS[(Math.random() * CHARS.size).toInt()] }
      .joinToString("")
  }

}