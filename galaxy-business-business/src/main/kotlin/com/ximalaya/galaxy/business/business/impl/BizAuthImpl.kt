package com.ximalaya.galaxy.business.business.impl

import com.ximalaya.galaxy.business.business.BizAuth
import com.ximalaya.galaxy.business.business.BusinessComponent
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.support.GalaxyAsserts
import com.ximalaya.galaxy.business.repo.entity.InvitationCodeEntity
import com.ximalaya.galaxy.business.repo.enums.InvitationCodeState
import com.ximalaya.galaxy.business.repo.service.InvitationCodeService
import com.ximalaya.galaxy.business.service.support.getUserInvitationCacheKey
import com.ximalaya.galaxy.business.support.InvitationCodeGenerator
import com.ximalaya.galaxy.business.vo.UserVo
import com.ximalaya.service.profile.converter.BasicUserInfoConverter
import com.ximalaya.service.profile.thrift.RemoteUserInfoQueryService
import org.redisson.api.RedissonClient
import org.redisson.client.codec.IntegerCodec
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

/**
 *<AUTHOR>
 *@create 2025-05-08 16:55
 */
@BusinessComponent
class BizAuthImpl(
  private val redissonClient: RedissonClient,
  private val userInfoQueryService: RemoteUserInfoQueryService.Iface,
  private val invitationCodeService: InvitationCodeService,
) : BizAuth {

  override fun getUser(uid: Long): UserVo {
    val userInfo = userInfoQueryService.getBasicUserInfo(uid)
    return UserVo.of(BasicUserInfoConverter.transform(userInfo))
  }

  override fun generateInvitationCode(count: Int): Boolean {
    if (count < 1) {
      return true
    }

    val existCodes = invitationCodeService.ktQuery().list()
      .map { it.code!! }
      .toMutableSet()

    val codes = InvitationCodeGenerator.generate(count, existCodes)

    val entities = InvitationCodeEntity.of(codes)

    return invitationCodeService.saveBatch(entities)
  }

  @Transactional(rollbackFor = [Throwable::class])
  override fun useInvitationCode(code: String, uid: Long): Boolean {
    val entity = invitationCodeService.ktQuery()
      .eq(InvitationCodeEntity::code, code)
      .eq(InvitationCodeEntity::codeState, InvitationCodeState.UNUSED.code)
      .one()

    GalaxyAsserts.assertNotNull(entity, ErrorCode.PARAMS_ERROR, "您的邀请码不存在或已被使用了哦~")

    GalaxyAsserts.assertTrue(
      invitationCodeService.ktUpdate()
        .eq(InvitationCodeEntity::code, code)
        .set(InvitationCodeEntity::uid, uid)
        .set(InvitationCodeEntity::codeState, InvitationCodeState.USED.code)
        .set(InvitationCodeEntity::updateTime, LocalDateTime.now())
        .update(), ErrorCode.ASSERT_ERROR, "验证码激活失败"
    );

    val invitationBucket = redissonClient.getBucket<Int>(getUserInvitationCacheKey(uid), IntegerCodec.INSTANCE)

    // redis存在
    if (invitationBucket.isExists) {
      GalaxyAsserts.assertTrue(invitationBucket.delete(), ErrorCode.ASSERT_ERROR, "验证码激活失败")
    }

    return true
  }

}